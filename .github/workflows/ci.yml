name: CI
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  tests:
    name: Run tests
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4

      - name: Set up .env files
        run: cp .env.dev.example .env.dev

      - name: Start db service
        run: docker compose up -d postgres

      - name: Run tests with coverage
        run: docker compose run --rm test bin/ci
