defmodule RepobotWeb.WebhookControllerTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Mox

  alias Repobot.Events.Event
  alias Repobot.PullRequest
  alias Repobot.Repo
  alias Repobot.Accounts.Organization

  setup :verify_on_exit!

  # Setup signature verifier mock for all tests
  setup do
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    :ok
  end

  describe "handle/2 for push events" do
    setup do
      user = create_user()
      %{user: user}
    end

    test "logs push events to the events table", %{conn: conn, user: user} do
      # Create a repository
      repo =
        create_repository(%{
          template: true,
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["some/path.ex"]
          }
        ]
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify that an event was logged
      events_after = Repo.all(Event)
      assert length(events_after) == event_count_before + 1

      # Get the latest event
      latest_event = Enum.max_by(events_after, & &1.inserted_at)
      assert latest_event.type == "github.push"
      assert latest_event.organization_id == user.default_organization_id
      assert latest_event.payload["repository"]["full_name"] == repo.full_name
    end

    test "handles push to template repository's default branch", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file using fixture
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "new content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock expectations
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that our single source file is passed
        [source_file_arg] = source_files
        assert source_file_arg.id == source_file.id
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # We don't need to validate the opts content, just make sure the function can handle the arg
        {:ok, "Files updated successfully"}
      end)

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify events were logged (push + sync events)
      events_after = Repo.all(Event)
      assert length(events_after) >= event_count_before + 2

      # Get the events by type
      push_events = Enum.filter(events_after, &(&1.type == "github.push"))
      sync_events = Enum.filter(events_after, &(&1.type == "repobot.sync"))

      # Verify push event details
      latest_push_event = Enum.max_by(push_events, & &1.inserted_at)
      assert latest_push_event.organization_id == template_repo.organization_id
      assert latest_push_event.payload["repository"]["full_name"] == template_repo.full_name

      # Verify sync event details
      latest_sync_event = Enum.max_by(sync_events, & &1.inserted_at)
      assert latest_sync_event.organization_id == target_repo.organization_id
      assert latest_sync_event.payload["template_repository_id"] == template_repo.id
      assert latest_sync_event.payload["target_repository_id"] == target_repo.id
      assert latest_sync_event.payload["result"] == "ok"
    end

    test "handles push with multiple files in template repository's default branch", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create first source file
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content 1",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create second source file
      source_file2 =
        create_source_file(%{
          name: "runtime.ex",
          target_path: "config/runtime.ex",
          content: "old content 2",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source files with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file2.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "new content 1", %{"sha" => "new-sha-1"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/runtime.ex" do
          {:ok, "new content 2", %{"sha" => "new-sha-2"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock expectations - expect a single call to sync_changes with both files
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that both source files are passed
        assert length(source_files) == 2
        assert Enum.any?(source_files, fn sf -> sf.id == source_file1.id end)
        assert Enum.any?(source_files, fn sf -> sf.id == source_file2.id end)
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        # We don't need to validate the opts content
        {:ok, "Files updated successfully"}
      end)

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Create push event payload with multiple files changed
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex", "config/runtime.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify events were logged (push + sync events)
      events_after = Repo.all(Event)
      assert length(events_after) >= event_count_before + 2

      # Verify push event has both file paths
      push_events = Enum.filter(events_after, &(&1.type == "github.push"))
      latest_push_event = Enum.max_by(push_events, & &1.inserted_at)

      commit = List.first(latest_push_event.payload["commits"])
      modified_files = commit["modified"]
      assert "config/config.ex" in modified_files
      assert "config/runtime.ex" in modified_files
    end

    test "ignores push to non-default branch", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/feature",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push to non-template repository", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push with no matching source files", %{conn: conn} do
      # Create user and repository using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["some/untracked/file.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles GitHub API error", %{conn: conn} do
      # Create user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file using fixture
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock to simulate error
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo
                            when owner == template_repo.owner and repo == template_repo.name ->
        :test_client
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:error, "API error"}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "modified" => ["config/config.ex"]
          }
        ]
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles invalid signature", %{conn: conn} do
      # Override the default stub to simulate invalid signature
      Repobot.Test.SignatureVerifierMock
      |> expect(:verify_signature, fn _conn -> {:error, "Invalid signature"} end)

      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => "some/repo",
          "default_branch" => "main"
        },
        "commits" => []
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert response(conn, 400) == "Bad Request"
    end

    test "preserves commit structure when multiple files are changed in a single commit", %{
      conn: conn
    } do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple source files
      source_file1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content 1",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file2 =
        create_source_file(%{
          name: "runtime.ex",
          target_path: "config/runtime.ex",
          content: "old content 2",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate both source files with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file1.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file2.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file2.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn
        :test_client, owner, repo, "config/config.ex", _commit_sha
        when owner == template_repo.owner and repo == template_repo.name ->
          {:ok, "new content 1", %{"sha" => "new-sha-1"}}
      end)
      |> expect(:get_file_content, fn
        :test_client, owner, repo, "config/runtime.ex", _commit_sha
        when owner == template_repo.owner and repo == template_repo.name ->
          {:ok, "new content 2", %{"sha" => "new-sha-2"}}
      end)

      # The critical part: Verify sync_changes is called with BOTH files in a SINGLE call
      # to preserve the original commit structure and message
      original_commit_message = "Update configuration files"
      commit_sha = "abc123"

      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn files, source_repo, target_repo_arg, :target_client, opts ->
        # Verify syncing both files together
        assert length(files) == 2
        assert Enum.any?(files, fn sf -> sf.id == source_file1.id end)
        assert Enum.any?(files, fn sf -> sf.id == source_file2.id end)
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # Verify using the original commit message
        assert Keyword.get(opts, :commit_message) == original_commit_message

        {:ok, "Files updated successfully"}
      end)

      # Create push event payload with a single commit modifying both files
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => commit_sha,
            "message" => original_commit_message,
            "added" => [],
            "modified" => ["config/config.ex", "config/runtime.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles push to template repository with template files", %{conn: conn} do
      # Create a user and repositories using fixtures
      user = create_user()

      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          data: %{"name" => "template-repo", "full_name" => "owner/template-repo"}
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id,
          data: %{"name" => "target-repo", "full_name" => "owner/target-repo"},
          settings: %{"greeting" => "Hello from target repo!"}
        })

      # Create a source file that is a template
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "# {{ name }} configuration\n\ngreeting = \"{{ settings.greeting }}\"",
          is_template: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Setup GitHub API mock expectations
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :test_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:get_file_content, fn client, owner, repo, path, commit_sha ->
        if client == :test_client and owner == template_repo.owner and
             repo == template_repo.name and path == "config/config.ex" do
          {:ok, "# {{ name }} configuration\n\ngreeting = \"{{ settings.greeting }}\"",
           %{"sha" => "new-sha"}}
        else
          raise "Unexpected arguments in get_file_content mock: #{inspect({client, owner, repo, path, commit_sha})}"
        end
      end)

      # Setup Sync mock to verify template rendering
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  source_repo,
                                  target_repo_arg,
                                  :target_client,
                                  _opts ->
        # Check that our source file is passed and is a template
        [source_file_arg] = source_files
        assert source_file_arg.id == source_file.id
        assert source_file_arg.is_template == true
        assert source_repo.id == template_repo.id
        assert target_repo_arg.id == target_repo.id

        # Let's bypass the mock and directly test rendering to verify it works
        # This simulates what would actually happen in the sync process
        {:ok, rendered_content} =
          Repobot.SourceFiles.render_template_for_repository(source_file_arg, target_repo_arg)

        # Verify the template was rendered with the target repo's variables
        assert rendered_content =~ "# target-repo configuration"
        assert rendered_content =~ "greeting = \"Hello from target repo!\""

        {:ok, "Files updated successfully"}
      end)

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "refreshes repository files on push to default branch with optimized file sync", %{
      conn: conn
    } do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create some repository files
      existing_file =
        create_repository_file(%{
          repository_id: repo.id,
          path: "lib/test.ex",
          name: "test.ex",
          type: "file",
          sha: "old-sha"
        })

      # Setup GitHub API mock to return file content for changed files only
      Repobot.Test.GitHubMock
      |> expect(:client, fn user_arg ->
        assert user_arg.id == user.id
        :test_client
      end)
      |> expect(:get_file_content, 2, fn :test_client, owner, repo_name, path ->
        assert owner == repo.owner
        assert repo_name == repo.name

        case path do
          "lib/test.ex" ->
            {:ok, "updated content", %{"size" => 200, "sha" => "new-sha"}}

          "lib/new_file.ex" ->
            {:ok, "new file content", %{"size" => 100, "sha" => "new-file-sha"}}
        end
      end)

      # Create push event payload to default branch with specific file changes
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update files",
            "added" => ["lib/new_file.ex"],
            "modified" => ["lib/test.ex"],
            "removed" => []
          }
        ]
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify push event was logged
      events_after = Repo.all(Event)
      assert length(events_after) >= event_count_before + 1

      # Verify push event details
      push_events = Enum.filter(events_after, &(&1.type == "github.push"))
      latest_push_event = Enum.max_by(push_events, & &1.inserted_at)
      assert latest_push_event.organization_id == user.default_organization_id
      assert latest_push_event.payload["repository"]["full_name"] == repo.full_name

      # Wait for the async task to complete and verify repository refresh event was logged
      assert_eventually(fn ->
        all_events = Repo.all(Event)
        refresh_events = Enum.filter(all_events, &(&1.type == "repobot.repository_refresh"))

        assert length(refresh_events) > 0, "No repository refresh events found"

        latest_refresh_event = Enum.max_by(refresh_events, & &1.inserted_at)
        assert latest_refresh_event.organization_id == repo.organization_id
        assert latest_refresh_event.payload["repository_id"] == repo.id
        assert latest_refresh_event.payload["trigger"] == "push_webhook"
        assert latest_refresh_event.payload["changed_files_count"] == 2
      end)

      # Verify that only the changed files were processed (not a full tree refresh)
      assert_eventually(fn ->
        # The existing file should be updated
        updated_existing_file = Repo.get!(Repobot.RepositoryFile, existing_file.id)
        assert updated_existing_file.content == "updated content"
        assert updated_existing_file.sha == "new-sha"

        # Check if new file was created
        new_file =
          Repo.get_by(Repobot.RepositoryFile, repository_id: repo.id, path: "lib/new_file.ex")

        assert new_file != nil
        assert new_file.content == "new file content"
        assert new_file.sha == "new-file-sha"
      end)
    end

    test "handles repository file refresh failure gracefully", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload to default branch
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update files",
            "modified" => ["lib/test.ex"]
          }
        ]
      }

      # Make the webhook request - should still succeed despite refresh failure
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "skips repository file refresh for non-default branch push", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload to feature branch
      payload = %{
        "ref" => "refs/heads/feature",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update files",
            "modified" => ["lib/test.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repository refresh events were logged
      refresh_events =
        Repo.all(Event)
        |> Enum.filter(&(&1.type == "repobot.repository_refresh"))

      assert length(refresh_events) == 0
    end

    test "handles file removal in push events", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a repository file that will be removed
      file_to_remove =
        create_repository_file(%{
          repository_id: repo.id,
          path: "lib/old_file.ex",
          name: "old_file.ex",
          type: "file",
          sha: "old-sha"
        })

      # Create push event payload with file removal
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Remove old file",
            "added" => [],
            "modified" => [],
            "removed" => ["lib/old_file.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Wait for the async task to complete and verify the file was removed
      assert_eventually(fn ->
        removed_file = Repo.get(Repobot.RepositoryFile, file_to_remove.id)
        assert removed_file == nil
      end)
    end

    test "handles mixed file operations in single push", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create existing files
      existing_file =
        create_repository_file(%{
          repository_id: repo.id,
          path: "lib/existing.ex",
          name: "existing.ex",
          type: "file",
          sha: "old-sha"
        })

      file_to_remove =
        create_repository_file(%{
          repository_id: repo.id,
          path: "lib/to_remove.ex",
          name: "to_remove.ex",
          type: "file",
          sha: "remove-sha"
        })

      # Setup GitHub API mock for the files that need content
      Repobot.Test.GitHubMock
      |> expect(:client, fn user_arg ->
        assert user_arg.id == user.id
        :test_client
      end)
      |> expect(:get_file_content, 2, fn :test_client, owner, repo_name, path ->
        assert owner == repo.owner
        assert repo_name == repo.name

        case path do
          "lib/existing.ex" ->
            {:ok, "updated existing content", %{"size" => 250, "sha" => "updated-sha"}}

          "lib/new.ex" ->
            {:ok, "new file content", %{"size" => 150, "sha" => "new-sha"}}
        end
      end)

      # Create push event with mixed operations
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Mixed file operations",
            "added" => ["lib/new.ex"],
            "modified" => ["lib/existing.ex"],
            "removed" => ["lib/to_remove.ex"]
          }
        ]
      }

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "push")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Wait for the async task to complete and verify all file operations
      assert_eventually(fn ->
        # Verify existing file was updated
        updated_existing = Repo.get!(Repobot.RepositoryFile, existing_file.id)
        assert updated_existing.content == "updated existing content"
        assert updated_existing.sha == "updated-sha"

        # Verify new file was created
        new_file = Repo.get_by(Repobot.RepositoryFile, repository_id: repo.id, path: "lib/new.ex")
        assert new_file != nil
        assert new_file.content == "new file content"
        assert new_file.sha == "new-sha"

        # Verify file was removed
        removed_file = Repo.get(Repobot.RepositoryFile, file_to_remove.id)
        assert removed_file == nil
      end)
    end
  end

  describe "handle/2 for pull request events" do
    test "logs pull_request events to the events table", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create pull request event payload
      payload = %{
        "action" => "opened",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repo.full_name
        }
      }

      # Count events before webhook
      event_count_before = length(Repo.all(Event))

      # Make the webhook request
      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify that an event was logged
      events_after = Repo.all(Event)
      assert length(events_after) == event_count_before + 1

      # Get the latest event
      latest_event = Enum.max_by(events_after, & &1.inserted_at)
      assert latest_event.type == "github.pull_request.opened"
      assert latest_event.organization_id == user.default_organization_id
      assert latest_event.payload["action"] == "opened"
      assert latest_event.payload["pull_request"]["number"] == 123
    end

    test "handles pull request event for non-existent repository", %{conn: conn} do
      payload = %{
        "action" => "closed",
        "pull_request" => %{
          "number" => 123,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => true
        },
        "repository" => %{
          "full_name" => "non/existent"
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "handles pull request event with no status change needed", %{conn: conn} do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a source file for the PR
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "test.ex",
          content: "test content",
          source_repository_id: repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a pull request
      pull_request =
        create_pull_request(%{
          repository: repo.full_name,
          pull_request_number: 123,
          status: "open",
          source_file_id: source_file.id
        })

      # Create payload for a "synchronize" action (which shouldn't change status)
      payload = %{
        "action" => "synchronize",
        "pull_request" => %{
          "number" => pull_request.pull_request_number,
          "title" => "Test PR",
          "html_url" => "https://github.com/owner/repo/pull/123",
          "merged" => false
        },
        "repository" => %{
          "full_name" => repo.full_name
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "pull_request")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify pull request status hasn't changed
      updated_pr = Repo.get!(PullRequest, pull_request.id)
      assert updated_pr.status == "open"
    end

    test "handles all pull request status transitions" do
      # Create a user and repository
      user = create_user()

      repo =
        create_repository(%{
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a source file for the PRs
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "test.ex",
          content: "test content",
          source_repository_id: repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      test_cases = [
        # {initial_status, action, merged, expected_status}
        {"open", "closed", true, "merged"},
        {"open", "closed", false, "closed"},
        {"closed", "reopened", false, "open"},
        {"merged", "reopened", true, "open"}
      ]

      for {initial_status, action, merged, expected_status} <- test_cases do
        # Create a pull request with initial status
        pull_request =
          create_pull_request(%{
            repository: repo.full_name,
            pull_request_number: System.unique_integer([:positive]),
            status: initial_status,
            source_file_id: source_file.id
          })

        # Create payload
        payload = %{
          "action" => action,
          "pull_request" => %{
            "number" => pull_request.pull_request_number,
            "title" => "Test PR",
            "html_url" =>
              "https://github.com/owner/repo/pull/#{pull_request.pull_request_number}",
            "merged" => merged
          },
          "repository" => %{
            "full_name" => repo.full_name
          }
        }

        conn =
          build_conn()
          |> put_req_header("content-type", "application/json")
          |> put_req_header("x-github-event", "pull_request")
          |> post("/hooks", payload)

        assert json_response(conn, 200) == %{"status" => "ok"}

        # Verify pull request status has changed by querying the database directly
        updated_pr = Repo.get!(PullRequest, pull_request.id)

        assert updated_pr.status == expected_status,
               "Expected PR status to change from #{initial_status} to #{expected_status} when action=#{action} and merged=#{merged}"
      end
    end
  end

  describe "repository creation events" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "successfully creates repository in database and broadcasts event", %{
      conn: conn,
      org: org
    } do
      # Repository data from GitHub webhook
      repository_data = %{
        "name" => "new-repo",
        "full_name" => "#{org.name}/new-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A new repository",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was created in database
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before + 1

      # Find the created repository
      created_repo = Enum.find(repos_after, &(&1.full_name == "#{org.name}/new-repo"))
      assert created_repo != nil
      assert created_repo.name == "new-repo"
      assert created_repo.owner == org.name
      assert created_repo.language == "Elixir"
      assert created_repo.fork == false
      assert created_repo.private == true
      assert created_repo.organization_id == org.id
      assert created_repo.data == repository_data

      # Verify PubSub broadcast was sent
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "new-repo"
      assert broadcast_data.full_name == "#{org.name}/new-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == true
      assert broadcast_data.description == "A new repository"
      assert broadcast_data.default_branch == "main"
    end

    test "handles organization not found error and still broadcasts event", %{conn: conn} do
      # Repository data with non-existent owner
      repository_data = %{
        "name" => "new-repo",
        "full_name" => "nonexistent/new-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A new repository",
        "default_branch" => "main",
        "owner" => %{
          "login" => "nonexistent"
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast still happens
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repository was created in database
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify PubSub broadcast was still sent despite error
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "new-repo"
      assert broadcast_data.full_name == "nonexistent/new-repo"
      assert broadcast_data.owner == "nonexistent"
      assert broadcast_data.private == true
      assert broadcast_data.description == "A new repository"
      assert broadcast_data.default_branch == "main"
    end

    test "handles repository already exists and still broadcasts event", %{conn: conn, org: org} do
      # Create an existing repository
      existing_repo =
        create_repository(%{
          name: "existing-repo",
          full_name: "#{org.name}/existing-repo",
          organization_id: org.id
        })

      # Repository data for the same repository
      repository_data = %{
        "name" => "existing-repo",
        "full_name" => "#{org.name}/existing-repo",
        "language" => "JavaScript",
        "fork" => false,
        "private" => false,
        "description" => "Updated description",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no new repository was created
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify existing repository wasn't modified
      unchanged_repo = Repo.get!(Repobot.Repository, existing_repo.id)
      assert unchanged_repo.name == existing_repo.name
      assert unchanged_repo.full_name == existing_repo.full_name

      # Verify PubSub broadcast was sent
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == "existing-repo"
      assert broadcast_data.full_name == "#{org.name}/existing-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == false
      assert broadcast_data.description == "Updated description"
      assert broadcast_data.default_branch == "main"
    end

    test "handles database creation failure and still broadcasts event", %{conn: conn, org: org} do
      # Repository data with invalid attributes to trigger changeset error
      repository_data = %{
        # Invalid name to trigger validation error
        "name" => nil,
        "full_name" => "#{org.name}/invalid-repo",
        "language" => "Elixir",
        "fork" => false,
        "private" => true,
        "description" => "A repository with invalid data",
        "default_branch" => "main",
        "owner" => %{
          "login" => org.name
        }
      }

      payload = %{
        "action" => "created",
        "repository" => repository_data
      }

      # Subscribe to PubSub to verify broadcast still happens
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repository was created in database due to validation error
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before

      # Verify PubSub broadcast was still sent despite database error
      assert_receive {:repository_created, broadcast_data}
      assert broadcast_data.name == nil
      assert broadcast_data.full_name == "#{org.name}/invalid-repo"
      assert broadcast_data.owner == org.name
      assert broadcast_data.private == true
      assert broadcast_data.description == "A repository with invalid data"
      assert broadcast_data.default_branch == "main"
    end
  end

  describe "installation events" do
    setup do
      # Create a test organization
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "updates organization installation_id when installation is created", %{
      conn: conn,
      org: org
    } do
      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)

      # Verify organization was updated
      updated_org = Repo.get(Organization, org.id)
      assert updated_org.installation_id == 123_456
    end

    test "removes organization installation_id when installation is deleted", %{
      conn: conn,
      org: org
    } do
      # First set an installation_id
      {:ok, org} = Repo.update(Organization.changeset(org, %{installation_id: 123_456}))
      assert org.installation_id == 123_456

      # Simulate GitHub installation webhook payload
      payload = %{
        "action" => "deleted",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => org.name,
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)

      # Verify installation_id was removed
      updated_org = Repo.get(Organization, org.id)
      assert is_nil(updated_org.installation_id)
    end

    test "ignores installation events for unknown organizations", %{conn: conn} do
      # Simulate GitHub installation webhook payload for unknown org
      payload = %{
        "action" => "created",
        "installation" => %{
          "id" => 123_456,
          "account" => %{
            "login" => "unknown-org",
            "type" => "User"
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "installation")
        |> post("/hooks", payload)

      assert response(conn, 200)
    end
  end

  describe "repository deletion events" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "successfully deletes repository from database when GitHub repository is deleted", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a repository with GitHub data including ID
      github_id = 123_456_789

      repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Test repository"
      }

      repo =
        create_repository(%{
          name: "test-repo",
          full_name: "#{org.name}/test-repo",
          owner: org.name,
          data: repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create some associated data to test cleanup
      source_file =
        create_source_file(%{
          name: "test.ex",
          target_path: "test.ex",
          content: "test content",
          source_repository_id: repo.id,
          user_id: user.id,
          organization_id: org.id
        })

      # Associate source file with repository
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: repo.id,
        source_file_id: source_file.id
      })

      # Create repository files
      create_repository_file(%{
        repository_id: repo.id,
        path: "test.ex",
        name: "test.ex",
        type: "file"
      })

      # Create an event related to this repository
      {:ok, _event} =
        Repobot.Events.log_github_event(
          "push",
          %{"repository" => repository_data},
          org.id,
          user.id,
          repo.id
        )

      # Count repositories and events before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))
      event_count_before = length(Repo.all(Repobot.Events.Event))

      # Create repository deletion webhook payload
      payload = %{
        "action" => "deleted",
        "repository" => repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was deleted from database
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before - 1
      assert Repo.get(Repobot.Repository, repo.id) == nil

      # Verify associated data was cleaned up
      # Repository files should be deleted by cascade
      assert Repo.get_by(Repobot.RepositoryFile, repository_id: repo.id) == nil

      # Source file associations should be cleaned up
      assert Repo.get_by(Repobot.RepositorySourceFile, repository_id: repo.id) == nil

      # Events related to the repository should be deleted
      events_after = Repo.all(Repobot.Events.Event)
      assert length(events_after) < event_count_before
      assert Repo.get_by(Repobot.Events.Event, repository_id: repo.id) == nil

      # Source file itself should still exist (not deleted)
      assert Repo.get(Repobot.SourceFile, source_file.id) != nil
    end

    test "handles repository not found in database gracefully", %{conn: conn} do
      # Create repository deletion webhook payload for non-existent repository
      github_id = 999_999_999

      repository_data = %{
        "id" => github_id,
        "name" => "non-existent-repo",
        "full_name" => "owner/non-existent-repo",
        "owner" => %{"login" => "owner"},
        "private" => false,
        "description" => "Non-existent repository"
      }

      payload = %{
        "action" => "deleted",
        "repository" => repository_data
      }

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repositories were affected
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before
    end

    test "handles database deletion error gracefully", %{conn: conn, user: user, org: org} do
      # Create a repository with GitHub data
      github_id = 123_456_789

      repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Test repository"
      }

      _repo =
        create_repository(%{
          name: "test-repo",
          full_name: "#{org.name}/test-repo",
          owner: org.name,
          data: repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create repository deletion webhook payload
      payload = %{
        "action" => "deleted",
        "repository" => repository_data
      }

      # Mock the Repo.delete to simulate a database error
      # Note: This is a simplified test - in a real scenario you might use Mox
      # or other mocking strategies to simulate database failures

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      # Even with potential errors, webhook should return 200 or 400 (not crash)
      assert conn.status in [200, 400]
    end

    test "deletes template repository with folder associations", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a template repository with GitHub data
      github_id = 123_456_789

      repository_data = %{
        "id" => github_id,
        "name" => "template-repo",
        "full_name" => "#{org.name}/template-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Template repository"
      }

      template_repo =
        create_repository(%{
          name: "template-repo",
          full_name: "#{org.name}/template-repo",
          owner: org.name,
          template: true,
          data: repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create folders and associate with template repository
      folder1 = create_folder(%{name: "Frontend", user_id: user.id, organization_id: org.id})
      folder2 = create_folder(%{name: "Backend", user_id: user.id, organization_id: org.id})

      # Add template repository to folders
      Repo.insert!(%Repobot.RepositoryFolder{
        repository_id: template_repo.id,
        folder_id: folder1.id
      })

      Repo.insert!(%Repobot.RepositoryFolder{
        repository_id: template_repo.id,
        folder_id: folder2.id
      })

      # Count repositories and associations before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))
      folder_assoc_count_before = length(Repo.all(Repobot.RepositoryFolder))

      # Create repository deletion webhook payload
      payload = %{
        "action" => "deleted",
        "repository" => repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was deleted
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before - 1
      assert Repo.get(Repobot.Repository, template_repo.id) == nil

      # Verify folder associations were cleaned up
      folder_assocs_after = Repo.all(Repobot.RepositoryFolder)
      assert length(folder_assocs_after) < folder_assoc_count_before
      assert Repo.get_by(Repobot.RepositoryFolder, repository_id: template_repo.id) == nil

      # Verify folders themselves still exist
      assert Repo.get(Repobot.Folder, folder1.id) != nil
      assert Repo.get(Repobot.Folder, folder2.id) != nil
    end
  end

  describe "repository edit events" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "successfully updates repository when name is changed", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "old-repo-name",
        "full_name" => "#{org.name}/old-repo-name",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          name: "old-repo-name",
          full_name: "#{org.name}/old-repo-name",
          owner: org.name,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "new-repo-name",
        "full_name" => "#{org.name}/new-repo-name",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated in database
      updated_repo = Repo.get(Repobot.Repository, repo.id)
      assert updated_repo.name == "new-repo-name"
      assert updated_repo.full_name == "#{org.name}/new-repo-name"
      assert updated_repo.owner == org.name
      assert updated_repo.data["name"] == "new-repo-name"
      assert updated_repo.data["full_name"] == "#{org.name}/new-repo-name"
    end

    test "successfully updates repository when moved to different owner", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          name: "test-repo",
          full_name: "#{org.name}/test-repo",
          owner: org.name,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create updated repository data with new owner
      new_owner = "new-owner"

      updated_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{new_owner}/test-repo",
        "owner" => %{"login" => new_owner},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated in database
      updated_repo = Repo.get(Repobot.Repository, repo.id)
      assert updated_repo.name == "test-repo"
      assert updated_repo.full_name == "#{new_owner}/test-repo"
      assert updated_repo.owner == new_owner
      assert updated_repo.data["owner"]["login"] == new_owner
      assert updated_repo.data["full_name"] == "#{new_owner}/test-repo"
    end

    test "successfully updates repository metadata (description, visibility, etc.)", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          name: "test-repo",
          full_name: "#{org.name}/test-repo",
          owner: org.name,
          private: false,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create updated repository data with changed metadata
      updated_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => true,
        "description" => "Updated description with new information",
        "language" => "JavaScript",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated in database
      updated_repo = Repo.get(Repobot.Repository, repo.id)
      assert updated_repo.name == "test-repo"
      assert updated_repo.full_name == "#{org.name}/test-repo"
      assert updated_repo.owner == org.name
      assert updated_repo.private == true
      assert updated_repo.language == "JavaScript"
      assert updated_repo.data["description"] == "Updated description with new information"
      assert updated_repo.data["private"] == true
      assert updated_repo.data["language"] == "JavaScript"
    end

    test "handles repository not found in database gracefully", %{conn: conn} do
      # Create repository edit webhook payload for non-existent repository
      github_id = 999_999_999

      repository_data = %{
        "id" => github_id,
        "name" => "non-existent-repo",
        "full_name" => "owner/non-existent-repo",
        "owner" => %{"login" => "owner"},
        "private" => false,
        "description" => "Non-existent repository",
        "language" => "Elixir",
        "fork" => false
      }

      payload = %{
        "action" => "edited",
        "repository" => repository_data
      }

      # Count repositories before webhook
      repo_count_before = length(Repo.all(Repobot.Repository))

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify no repositories were affected
      repos_after = Repo.all(Repobot.Repository)
      assert length(repos_after) == repo_count_before
    end

    test "handles database update error gracefully", %{conn: conn, user: user, org: org} do
      # Create a repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "test-repo",
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          name: "test-repo",
          full_name: "#{org.name}/test-repo",
          owner: org.name,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create updated repository data with invalid name (nil)
      updated_repository_data = %{
        "id" => github_id,
        "name" => nil,
        "full_name" => "#{org.name}/test-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Test repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      # Even with database errors, webhook should return 200 or 400 (not crash)
      assert conn.status in [200, 400]

      # Verify original repository data is unchanged
      unchanged_repo = Repo.get(Repobot.Repository, repo.id)
      assert unchanged_repo.name == "test-repo"
      assert unchanged_repo.full_name == "#{org.name}/test-repo"
    end

    test "updates template repository while preserving template status", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a template repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "template-repo",
        "full_name" => "#{org.name}/template-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Template repository",
        "language" => "Elixir",
        "fork" => false
      }

      template_repo =
        create_repository(%{
          name: "template-repo",
          full_name: "#{org.name}/template-repo",
          owner: org.name,
          template: true,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create folders and associate with template repository
      folder = create_folder(%{name: "Frontend", user_id: user.id, organization_id: org.id})

      Repo.insert!(%Repobot.RepositoryFolder{
        repository_id: template_repo.id,
        folder_id: folder.id
      })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "renamed-template-repo",
        "full_name" => "#{org.name}/renamed-template-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Renamed template repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository edit webhook payload
      payload = %{
        "action" => "edited",
        "repository" => updated_repository_data
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated but template status preserved
      updated_repo = Repo.get(Repobot.Repository, template_repo.id)
      assert updated_repo.name == "renamed-template-repo"
      assert updated_repo.full_name == "#{org.name}/renamed-template-repo"
      assert updated_repo.template == true
      assert updated_repo.data["name"] == "renamed-template-repo"
      assert updated_repo.data["description"] == "Renamed template repository"

      # Verify folder associations are preserved
      folder_assoc = Repo.get_by(Repobot.RepositoryFolder, repository_id: template_repo.id)
      assert folder_assoc != nil
      assert folder_assoc.folder_id == folder.id
    end
  end

  describe "repository rename events" do
    setup do
      user = create_user(%{login: "testuser"})
      org = user.default_organization
      %{user: user, org: org}
    end

    test "successfully updates repository when renamed", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "old-repo-name",
        "full_name" => "#{org.name}/old-repo-name",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      repo =
        create_repository(%{
          name: "old-repo-name",
          full_name: "#{org.name}/old-repo-name",
          owner: org.name,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "new-repo-name",
        "full_name" => "#{org.name}/new-repo-name",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original description",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository rename webhook payload
      payload = %{
        "action" => "renamed",
        "repository" => updated_repository_data,
        "changes" => %{
          "repository" => %{
            "name" => %{
              "from" => "old-repo-name"
            }
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated
      updated_repo = Repo.get(Repobot.Repository, repo.id)
      assert updated_repo.name == "new-repo-name"
      assert updated_repo.full_name == "#{org.name}/new-repo-name"
      assert updated_repo.data["name"] == "new-repo-name"
      assert updated_repo.data["full_name"] == "#{org.name}/new-repo-name"
    end

    test "handles rename when repository not found in database", %{conn: conn, org: org} do
      # Create repository rename webhook payload for non-existent repository
      github_id = 999_999_999

      updated_repository_data = %{
        "id" => github_id,
        "name" => "new-repo-name",
        "full_name" => "#{org.name}/new-repo-name",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Some description",
        "language" => "Elixir",
        "fork" => false
      }

      payload = %{
        "action" => "renamed",
        "repository" => updated_repository_data,
        "changes" => %{
          "repository" => %{
            "name" => %{
              "from" => "old-repo-name"
            }
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      # Should still return success even if repository not found
      assert json_response(conn, 200) == %{"status" => "ok"}
    end

    test "successfully updates template repository when renamed", %{
      conn: conn,
      user: user,
      org: org
    } do
      # Create a template repository with GitHub data
      github_id = 123_456_789

      original_repository_data = %{
        "id" => github_id,
        "name" => "old-template-repo",
        "full_name" => "#{org.name}/old-template-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Original template description",
        "language" => "Elixir",
        "fork" => false
      }

      template_repo =
        create_repository(%{
          name: "old-template-repo",
          full_name: "#{org.name}/old-template-repo",
          owner: org.name,
          template: true,
          data: original_repository_data,
          user_id: user.id,
          organization_id: org.id
        })

      # Create folders and associate with template repository
      folder = create_folder(%{name: "Frontend", user_id: user.id, organization_id: org.id})

      Repo.insert!(%Repobot.RepositoryFolder{
        repository_id: template_repo.id,
        folder_id: folder.id
      })

      # Create updated repository data with new name
      updated_repository_data = %{
        "id" => github_id,
        "name" => "renamed-template-repo",
        "full_name" => "#{org.name}/renamed-template-repo",
        "owner" => %{"login" => org.name},
        "private" => false,
        "description" => "Renamed template repository",
        "language" => "Elixir",
        "fork" => false
      }

      # Create repository rename webhook payload
      payload = %{
        "action" => "renamed",
        "repository" => updated_repository_data,
        "changes" => %{
          "repository" => %{
            "name" => %{
              "from" => "old-template-repo"
            }
          }
        }
      }

      conn =
        conn
        |> put_req_header("content-type", "application/json")
        |> put_req_header("x-github-event", "repository")
        |> post("/hooks", payload)

      assert json_response(conn, 200) == %{"status" => "ok"}

      # Verify repository was updated but template status preserved
      updated_repo = Repo.get(Repobot.Repository, template_repo.id)
      assert updated_repo.name == "renamed-template-repo"
      assert updated_repo.full_name == "#{org.name}/renamed-template-repo"
      assert updated_repo.template == true
      assert updated_repo.data["name"] == "renamed-template-repo"
      assert updated_repo.data["description"] == "Renamed template repository"

      # Verify folder associations are preserved
      folder_assoc = Repo.get_by(Repobot.RepositoryFolder, repository_id: template_repo.id)
      assert folder_assoc != nil
      assert folder_assoc.folder_id == folder.id
    end
  end
end
