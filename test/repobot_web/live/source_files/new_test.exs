defmodule RepobotWeb.Live.SourceFiles.NewTest do
  use RepobotWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Repobot.Test.Fixtures

  setup do
    user = create_user()
    conn = init_test_session(build_conn(), %{current_user_id: user.id})

    {:ok, conn: conn, user: user}
  end

  describe "new source file" do
    test "renders the new source file form", %{conn: conn} do
      {:ok, view, html} = live(conn, ~p"/source-files/new")

      # Verify page title and description
      assert html =~ "New Source File"
      assert html =~ "Create a new source file that can be synced across repositories"

      # Verify form fields
      assert has_element?(view, "input#source-file-name")
      assert has_element?(view, "input#source-file-target-path")
      assert has_element?(view, "input#source-file-is-template")
      assert has_element?(view, "textarea")
    end

    test "navigates back to source files list", %{conn: conn} do
      {:ok, view, _html} = live(conn, ~p"/source-files/new")

      # Click the Source Files link in breadcrumbs
      {:ok, _view, html} =
        view
        |> element("a", "Source Files")
        |> render_click()
        |> follow_redirect(conn)

      assert html =~ "Source Files"
      assert html =~ "A list of all your source files"
    end

    test "creates source file with valid data", %{conn: conn, user: user} do
      {:ok, view, _html} = live(conn, ~p"/source-files/new")

      # Count source files before submission
      initial_count = length(Repobot.SourceFiles.list_source_files(user))

      # First update the tags
      view
      |> element("input[name=\"source_file[tags]\"]")
      |> render_change(%{"source_file" => %{"tags" => "elixir, test"}})

      # Submit the form with valid data
      {:ok, _view, html} =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: "test_file.ex",
            content: "defmodule TestFile do\n  def hello, do: :world\nend\n",
            target_path: "lib/test_file.ex"
            # is_template is handled by the checkbox directly
          }
        })
        |> render_submit()
        |> follow_redirect(conn)

      # Verify we're redirected to the source files index
      assert html =~ "Source Files"
      assert html =~ "Source file created successfully"

      # Verify a new source file was created
      assert length(Repobot.SourceFiles.list_source_files(user)) == initial_count + 1

      # Verify the new source file has the correct attributes
      new_file =
        Repobot.SourceFiles.list_source_files(user)
        |> Enum.find(&(&1.name == "test_file.ex"))

      assert new_file
      assert new_file.target_path == "lib/test_file.ex"
      assert new_file.content == "defmodule TestFile do\n  def hello, do: :world\nend\n"
      assert new_file.user_id == user.id
      assert new_file.organization_id == user.default_organization_id

      # Verify tags were created and associated
      assert Enum.any?(new_file.tags, &(&1.name == "elixir"))
      assert Enum.any?(new_file.tags, &(&1.name == "test"))
    end

    test "shows validation errors with invalid data", %{conn: conn} do
      {:ok, view, _html} = live(conn, ~p"/source-files/new")

      # Submit the form with invalid data (missing name)
      rendered =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: "",
            content: "Some content",
            target_path: "lib/file.ex"
          }
        })
        |> render_submit()

      # Verify error message is displayed
      assert rendered =~ "can&#39;t be blank"
    end

    test "creates a template source file", %{conn: conn, user: user} do
      {:ok, view, _html} = live(conn, ~p"/source-files/new")

      # First update the tags
      view
      |> element("input[name=\"source_file[tags]\"]")
      |> render_change(%{"source_file" => %{"tags" => "documentation, template"}})

      # Toggle the template checkbox
      view
      |> element("#source-file-is-template")
      |> render_click()

      # Submit the form with template data
      {:ok, _view, html} =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: "readme.md",
            content: "# {{ name }}\n\n{{ description }}\n\nMaintained by {{ owner }}",
            target_path: "README.md"
            # is_template is handled by the checkbox directly
          }
        })
        |> render_submit()
        |> follow_redirect(conn)

      # Verify we're redirected to the source files index
      assert html =~ "Source Files"
      assert html =~ "Source file created successfully"

      # Verify the new template source file has the correct attributes
      new_file =
        Repobot.SourceFiles.list_source_files(user)
        |> Enum.find(&(&1.name == "readme.md"))

      assert new_file
      assert new_file.is_template == true
      assert new_file.target_path == "README.md"
      assert new_file.content =~ "{{ name }}"
      assert new_file.content =~ "{{ description }}"
    end
  end
end
