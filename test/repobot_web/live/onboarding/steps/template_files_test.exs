defmodule RepobotWeb.Live.Onboarding.Steps.TemplateFilesTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest
  import Mox

  alias Repobot.Test.GitHubMock
  alias Repobot.Accounts.User.Settings

  setup :verify_on_exit!

  describe "template files step" do
    setup do
      user = create_user()
      user = %{user | settings: %Settings{onboarding_completed: false}}

      # Create template repository (empty - will need refresh)
      template_repo =
        create_repository(%{
          name: "template-repo",
          owner: user.login,
          full_name: "#{user.login}/template-repo",
          organization_id: user.default_organization_id,
          data: %{"default_branch" => "main"}
        })

      # Create target repositories with common files (already have files - should not need refresh)
      sync_repo_1 =
        create_repository(%{
          name: "sync-repo-1",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-1",
          organization_id: user.default_organization_id
        })

      sync_repo_2 =
        create_repository(%{
          name: "sync-repo-2",
          owner: user.login,
          full_name: "#{user.login}/sync-repo-2",
          organization_id: user.default_organization_id
        })

      # Create common files in both target repositories
      common_content = "This is common content."

      create_repository_file(%{
        path: "common.txt",
        content: common_content,
        size: String.length(common_content),
        type: "file",
        sha: "common123",
        repository_id: sync_repo_1.id
      })

      create_repository_file(%{
        path: "common.txt",
        content: common_content,
        size: String.length(common_content),
        type: "file",
        sha: "common123",
        repository_id: sync_repo_2.id
      })

      # Preload files
      template_repo = Repobot.Repo.preload(template_repo, :files)
      sync_repo_1 = Repobot.Repo.preload(sync_repo_1, :files)
      sync_repo_2 = Repobot.Repo.preload(sync_repo_2, :files)

      {
        :ok,
        user: user,
        template_repo: template_repo,
        sync_repo_1: sync_repo_1,
        sync_repo_2: sync_repo_2,
        _common_content: common_content
      }
    end

    test "selecting a file does not trigger unnecessary refresh", %{
      conn: conn,
      user: user,
      template_repo: template_repo,
      sync_repo_1: sync_repo_1,
      sync_repo_2: sync_repo_2,
      _common_content: _common_content
    } do
      # Mock AI for tag inference in Summary step
      Repobot.Test.AIMock
      |> stub(:infer_tags, fn _source_file, _organization -> {:ok, []} end)

      # Mock GitHub API - we should only see these calls during initial load
      # and NOT when files are selected/deselected
      GitHubMock
      |> stub(:client, fn _user_param -> :test_client end)
      # Only template_repo (empty) will be refreshed, sync repos already have files
      |> expect(:get_tree, 1, fn :test_client, _owner, repo_name ->
        case repo_name do
          # Empty repository
          "template-repo" -> {:ok, []}
          _ -> raise "Unexpected repo refresh: #{repo_name}"
        end
      end)
      # Only sync_repo_1 and sync_repo_2 have files to refresh content for
      |> expect(:get_file_content, 2, fn :test_client, _owner, repo_name, path ->
        repo =
          cond do
            repo_name == sync_repo_1.name -> sync_repo_1
            repo_name == sync_repo_2.name -> sync_repo_2
            true -> raise "Unexpected repo: #{repo_name}"
          end

        file = Enum.find(repo.files, &(&1.path == path))

        if file do
          {:ok, file.content, %{}}
        else
          {:error, :file_not_found}
        end
      end)

      # Start onboarding and navigate to template files step
      {:ok, view, _html} =
        conn
        |> init_test_session(%{
          current_user_id: user.id,
          current_organization_id: user.default_organization_id
        })
        |> live(~p"/onboarding")

      # Navigate through steps to reach template files
      # Welcome -> Template Repository
      view |> element("button", "Next") |> render_click()
      view |> element("#select_existing") |> render_click()
      view |> element("input[value='#{template_repo.id}']") |> render_click()
      # Template Repository -> Repository Sync
      view |> element("button", "Next") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_1.id}']") |> render_click()
      view |> element("button[phx-value-id='#{sync_repo_2.id}']") |> render_click()
      # Repository Sync -> Template Files
      view |> element("button", "Next") |> render_click()

      # Wait for common files to be loaded
      assert_eventually(fn ->
        refute has_element?(view, "p", "Loading common files...")
      end)

      # Verify the common file is found and listed
      assert_eventually(fn ->
        assert has_element?(view, "ul[data-testid='common-files-list']")
        assert has_element?(view, "span[data-testid='common-file-name']", "common.txt")
      end)

      # Now select the file - this should NOT trigger any additional GitHub API calls
      # The mock will fail if more than the expected 3 calls are made
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is selected
      assert has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Deselect the file - this should also NOT trigger additional API calls
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is deselected
      refute has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Select it again
      view |> element("input[phx-value-path='common.txt']") |> render_click()

      # Verify the file is selected again
      assert has_element?(view, "input[phx-value-path='common.txt'][checked]")

      # Proceed to next step - this should finalize the step
      view |> element("button", "Next") |> render_click()

      # Verify we moved to the summary step
      assert_eventually(fn ->
        assert has_element?(view, "h2", "Setup Summary")
      end)
    end
  end
end
