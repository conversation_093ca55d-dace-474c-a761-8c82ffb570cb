<div class="p-4">
  <div class="mb-6">
    <h1 class="text-2xl font-semibold text-slate-900">Repositories</h1>
    <p class="mt-2 text-sm text-slate-600">Manage repositories in your Repobot instance.</p>
  </div>

  <div class="bg-white rounded-lg shadow mb-6 p-4">
    <h2 class="text-lg font-medium mb-4">Filters</h2>
    <form
      action={~p"/admin/repositories"}
      method="get"
      class="grid grid-cols-1 md:grid-cols-3 gap-4"
    >
      <div>
        <label class="block text-sm font-medium text-slate-700 mb-1">Organization</label>
        <select
          name="organization_id"
          class="w-full rounded-md border border-slate-300 px-3 py-2 text-slate-900 focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="">All Organizations</option>
          <%= for org <- @organizations do %>
            <option value={org.id} selected={@filters.organization_id == org.id}>
              {org.name}
            </option>
          <% end %>
        </select>
      </div>

      <div class="md:col-span-3 flex justify-end gap-2">
        <a
          href={~p"/admin/repositories"}
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50"
        >
          Clear Filters
        </a>
        <button
          type="submit"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700"
        >
          Apply Filters
        </button>
      </div>
    </form>
  </div>

  <div class="overflow-x-auto rounded-lg border border-slate-200">
    <table class="w-full divide-y divide-slate-200">
      <thead>
        <tr class="bg-slate-50">
          <th class="px-3 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Repository
          </th>
          <th class="px-3 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Organization
          </th>
          <th class="px-3 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Language
          </th>
          <th class="px-3 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Private
          </th>
          <th class="px-3 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Created
          </th>
          <th class="px-3 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        <%= for repo <- @repositories do %>
          <tr>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="flex items-center">
                <div class="text-sm font-medium text-slate-900">
                  {repo.name}
                </div>
              </div>
            </td>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                {repo.organization.name}
              </div>
            </td>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                {repo.language}
              </div>
            </td>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                <%= if repo.private do %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Yes
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                    No
                  </span>
                <% end %>
              </div>
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-sm text-slate-500">
              {Calendar.strftime(repo.inserted_at, "%Y-%m-%d")}
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <.link
                  navigate={~p"/admin/repositories/#{repo}"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  View
                </.link>
                <.link
                  navigate={~p"/admin/repositories/#{repo}/edit"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  Edit
                </.link>
                <.link
                  href={~p"/admin/repositories/#{repo}"}
                  method="delete"
                  data-confirm="Are you sure you want to delete this repository?"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </.link>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="mt-6 flex items-center justify-between">
    <div class="text-sm text-slate-500">
      Showing {length(@repositories)} of {@page.total_entries} repositories
    </div>
    <div class="flex gap-2">
      <%= if @page.page_number > 1 do %>
        <.link
          navigate={~p"/admin/repositories?" <> build_pagination_params(@filters, @page.page_number - 1)}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Previous
        </.link>
      <% end %>

      <%= if @page.page_number < @page.total_pages do %>
        <.link
          navigate={~p"/admin/repositories?" <> build_pagination_params(@filters, @page.page_number + 1)}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Next
        </.link>
      <% end %>
    </div>
  </div>
</div>
