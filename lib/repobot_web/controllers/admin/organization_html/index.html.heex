<div class="p-6">
  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Organizations</h1>
    <p class="mt-2 text-sm text-slate-600">Manage organizations in your Repobot instance.</p>
  </div>

  <div class="overflow-hidden rounded-lg border border-slate-200">
    <table class="min-w-full divide-y divide-slate-200">
      <thead>
        <tr class="bg-slate-50">
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Organization
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Installation ID
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Private Repos
          </th>
          <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
            Created
          </th>
          <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
            Actions
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-slate-200">
        <%= for org <- @organizations do %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div class="h-10 w-10 flex-shrink-0">
                  <%= if org.settings.avatar_url do %>
                    <img
                      class="h-10 w-10 rounded-full"
                      src={org.settings.avatar_url}
                      alt={org.name}
                    />
                  <% else %>
                    <div class="h-10 w-10 rounded-full bg-slate-200 flex items-center justify-center">
                      <.icon name="hero-building-office" class="h-5 w-5 text-slate-500" />
                    </div>
                  <% end %>
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-slate-900">
                    {org.name}
                  </div>
                  <%= if org.settings.html_url do %>
                    <div class="text-sm text-slate-500">
                      <a
                        href={org.settings.html_url}
                        target="_blank"
                        class="hover:text-indigo-600"
                      >
                        View on GitHub
                      </a>
                    </div>
                  <% end %>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                <%= if org.installation_id do %>
                  {org.installation_id}
                <% else %>
                  <span class="text-slate-500">Not installed</span>
                <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-slate-900">
                <%= if org.private_repos do %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Yes
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                    No
                  </span>
                <% end %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
              {Calendar.strftime(org.inserted_at, "%Y-%m-%d")}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <.link
                  navigate={~p"/admin/organizations/#{org}"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  View
                </.link>
                <.link
                  navigate={~p"/admin/organizations/#{org}/edit"}
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  Edit
                </.link>
                <.link
                  href={~p"/admin/organizations/#{org}"}
                  method="delete"
                  data-confirm="Are you sure you want to delete this organization?"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </.link>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <div class="mt-6 flex items-center justify-between">
    <div class="text-sm text-slate-500">
      Showing {length(@organizations)} of {@page.total_entries} organizations
    </div>
    <div class="flex gap-2">
      <%= if @page.page_number > 1 do %>
        <.link
          navigate={~p"/admin/organizations?page=#{@page.page_number - 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Previous
        </.link>
      <% end %>

      <%= if @page.page_number < @page.total_pages do %>
        <.link
          navigate={~p"/admin/organizations?page=#{@page.page_number + 1}"}
          class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
        >
          Next
        </.link>
      <% end %>
    </div>
  </div>
</div>
