<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/users"} class="hover:text-indigo-600">
          Users
        </.link>
      </li>
      <li>•</li>
      <li>
        <.link navigate={~p"/admin/users/#{@user}"} class="hover:text-indigo-600">
          {@user.login}
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">Edit</li>
    </ol>
  </nav>

  <div class="mb-8">
    <h1 class="text-2xl font-semibold text-slate-900">Edit User</h1>
    <p class="mt-2 text-sm text-slate-600">Update user information and settings.</p>
  </div>

  <div class="max-w-2xl">
    <.form :let={f} for={@changeset} action={~p"/admin/users/#{@user}"} method="put">
      <div class="bg-white rounded-lg shadow-sm border border-slate-200 divide-y divide-slate-200">
        <div class="p-6">
          <h2 class="text-lg font-medium text-slate-900 mb-4">Basic Information</h2>
          <div class="space-y-4">
            <div>
              <.input field={f[:login]} type="text" label="Login" />
            </div>
            <div>
              <.input field={f[:email]} type="email" label="Email" />
            </div>
            <div>
              <% org_options = Enum.map(@all_organizations, fn org -> {org.name, org.id} end) %>
              <.input
                field={f[:default_organization_id]}
                type="select"
                label="Default Organization"
                options={org_options}
                value={@user.default_organization_id}
              />
            </div>
          </div>
        </div>

        <div class="p-6 bg-slate-50 flex items-center justify-end gap-4">
          <.link
            navigate={~p"/admin/users/#{@user}"}
            class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
          >
            Cancel
          </.link>
          <button
            type="submit"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Save Changes
          </button>
        </div>
      </div>
    </.form>
  </div>
</div>
