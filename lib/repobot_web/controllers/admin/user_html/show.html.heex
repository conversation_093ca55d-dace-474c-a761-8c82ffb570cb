<div class="p-6">
  <nav class="mb-8">
    <ol role="list" class="flex items-center space-x-2 text-sm text-slate-500">
      <li>
        <.link navigate={~p"/admin/users"} class="hover:text-indigo-600">
          Users
        </.link>
      </li>
      <li>•</li>
      <li class="font-medium text-slate-900">{@user.login}</li>
    </ol>
  </nav>

  <div class="mb-8 flex items-center justify-between">
    <div>
      <h1 class="text-2xl font-semibold text-slate-900">{@user.info.name || @user.login}</h1>
      <p class="mt-2 text-sm text-slate-600">User details and information.</p>
    </div>
    <div class="flex gap-2">
      <.link
        navigate={~p"/admin/users/#{@user}/edit"}
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
      >
        Edit User
      </.link>
      <.link
        href={~p"/admin/users/#{@user}"}
        method="delete"
        data-confirm="Are you sure you want to delete this user?"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
      >
        Delete User
      </.link>
    </div>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Profile Information</h2>
      <div class="flex items-center gap-4 mb-6">
        <img src={@user.info.avatar_url} alt={@user.login} class="h-16 w-16 rounded-full" />
        <div>
          <div class="font-medium text-slate-900">{@user.info.name || @user.login}</div>
          <div class="text-sm text-slate-500">@{@user.login}</div>
        </div>
      </div>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Email</dt>
          <dd class="mt-1 text-sm text-slate-900">{@user.info.email}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Location</dt>
          <dd class="mt-1 text-sm text-slate-900">{@user.info.location || "Not specified"}</dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Bio</dt>
          <dd class="mt-1 text-sm text-slate-900">
            {@user.info.description || "No bio provided"}
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">GitHub Profile</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <a
              href={@user.info.html_url}
              target="_blank"
              class="text-indigo-600 hover:text-indigo-900"
            >
              {@user.info.html_url}
            </a>
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">Account Information</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Default Organization</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @user.default_organization do %>
              {@user.default_organization.name}
            <% else %>
              <span class="text-slate-500">None</span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Account Created</dt>
          <dd class="mt-1 text-sm text-slate-900">
            {Calendar.strftime(@user.inserted_at, "%B %d, %Y")}
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">Last Updated</dt>
          <dd class="mt-1 text-sm text-slate-900">
            {Calendar.strftime(@user.updated_at, "%B %d, %Y at %H:%M")}
          </dd>
        </div>
      </dl>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
      <h2 class="text-lg font-medium text-slate-900 mb-4">API Settings</h2>
      <dl class="space-y-4">
        <div>
          <dt class="text-sm font-medium text-slate-500">Anthropic API Key</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @user.settings.anthropic_api_key do %>
              <span class="text-green-600">Configured</span>
            <% else %>
              <span class="text-slate-500">Not configured</span>
            <% end %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-slate-500">OpenAI API Key</dt>
          <dd class="mt-1 text-sm text-slate-900">
            <%= if @user.settings.openai_api_key do %>
              <span class="text-green-600">Configured</span>
            <% else %>
              <span class="text-slate-500">Not configured</span>
            <% end %>
          </dd>
        </div>
      </dl>
    </div>
  </div>
</div>
