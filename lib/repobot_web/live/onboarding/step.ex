defmodule RepobotWeb.Live.Onboarding.Step do
  defmacro __using__(_opts) do
    quote do
      use RepobotWeb, :live_component

      import RepobotWeb.Live.Onboarding.Step

      alias Repobot.Repo
      alias Repobot.{Folders, Repositories}

      def assign_repositories(%{assigns: %{repositories: repos}} = socket) do
        socket
        |> assign(
          :folders,
          Folders.list_folders(
            socket.assigns.current_user,
            nil,
            socket.assigns.current_organization.id
          )
        )
        |> assign(:unorganized_repos, Enum.filter(repos, &is_nil(&1.folder_id)))
        |> assign(:repos_by_folder, Enum.group_by(repos, & &1.folder_id))
      end

      def maybe_load_repositories(socket) do
        if connected?(socket) do
          if Map.has_key?(socket.assigns, :repositories) do
            socket
          else
            organization_id = socket.assigns.current_organization.id
            user = socket.assigns.current_user

            repositories = Repositories.user_repositories(user, false, organization_id)

            {repositories, needs_refresh} =
              if Enum.empty?(repositories) do
                {Repositories.user_repositories(user, :refresh, organization_id), true}
              else
                {repositories, false}
              end

            socket
            |> assign(:repositories, repositories)
            |> assign(:refreshing, needs_refresh)
          end
        else
          socket |> assign(:repositories, [])
        end
      end
    end
  end

  def finalize(name, state) do
    send(self(), {:step_completed, {name, state}})
  end
end
