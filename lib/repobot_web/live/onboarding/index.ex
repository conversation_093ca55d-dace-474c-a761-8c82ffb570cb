defmodule RepobotWeb.Live.Onboarding.Index do
  use RepobotWeb, :live_view

  alias RepobotWeb.Live.Onboarding.Steps

  require Logger

  @steps [
    welcome: Steps.Welcome,
    template_repository: Steps.TemplateRepository,
    repository_sync: Steps.RepositorySync,
    template_files: Steps.TemplateFiles,
    summary: Steps.Summary
  ]

  @step_headers [
    welcome: "Welcome",
    template_repository: "Template Repository",
    repository_sync: "Repository Sync",
    template_files: "Template Files",
    summary: "Summary"
  ]

  defmodule Step do
    defstruct [:name, :component, :state]
  end

  def mount(_params, _session, socket) do
    steps = build_steps()

    # Subscribe to repository events if connected
    if connected?(socket) do
      Phoenix.PubSub.subscribe(Repobot.PubSub, "repository_events")
    end

    socket =
      socket
      |> assign(:current_step, get_step(:welcome, steps))
      |> assign(:steps, steps)
      |> assign(:state, fresh_state(steps))
      |> assign(:page_title, "Welcome to RepoBot")

    {:ok, socket}
  end

  def update(assigns, socket) do
    {:ok, assign(socket, assigns)}
  end

  attr :step_headers, :map, default: @step_headers

  def render(assigns) do
    ~H"""
    <div class="max-w-6xl mx-auto py-12">
      <div class="mb-8">
        <nav aria-label="Progress">
          <ol role="list" class="flex items-center justify-between">
            <%= for {step, index} <- Enum.with_index(@steps) do %>
              <li class="relative" data-step={step.name} data-testid={"onboarding-step-#{step.name}"}>
                <div class="flex items-center">
                  <span
                    class={[
                      "h-9 w-9 rounded-full flex items-center justify-center text-sm font-medium",
                      if step.name == @current_step.name do
                        "bg-indigo-600 text-white"
                      else
                        if index < Enum.find_index(@steps, &(&1.name == @current_step.name)) do
                          "bg-indigo-500 text-white"
                        else
                          "bg-slate-200 text-slate-700"
                        end
                      end
                    ]}
                    data-step-indicator={step.name}
                  >
                    {index + 1}
                  </span>
                  <span class="ml-4 text-sm font-medium text-slate-900">
                    {@step_headers[step.name]}
                  </span>
                </div>
              </li>
            <% end %>
          </ol>
        </nav>
      </div>

      <div class="bg-white shadow-sm rounded-lg border border-slate-200 p-8">
        <.live_component
          module={@current_step.component}
          id={@current_step.name}
          current_user={@current_user}
          current_organization={@current_organization}
          state={@state}
        />

        <div class="mt-8 flex justify-between">
          <button
            :if={@current_step.name != :welcome}
            phx-click="previous_step"
            class="inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-md text-slate-700 bg-white hover:bg-slate-50"
          >
            Previous
          </button>
          <div></div>
          <button
            :if={@current_step.name != :summary}
            phx-click="next_step"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Next
          </button>
        </div>
      </div>
    </div>
    """
  end

  def handle_event("next_step", _params, socket) do
    current_step = socket.assigns.current_step
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == current_step.name))
    next_step = Enum.at(socket.assigns.steps, current_index + 1)

    # Finalize the current step before proceeding
    case current_step.name do
      :template_files ->
        # Send finalize message to the template files component
        send_update(Steps.TemplateFiles, id: :template_files, finalize_step: true)
        {:noreply, socket}

      _ ->
        # For other steps, proceed directly
        {:noreply, assign(socket, :current_step, next_step)}
    end
  end

  def handle_event("previous_step", _params, socket) do
    current_step = socket.assigns.current_step
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == current_step.name))
    previous_step = Enum.at(socket.assigns.steps, current_index - 1)

    {:noreply, assign(socket, :current_step, previous_step)}
  end

  def handle_info({:step_completed, {name, state}}, socket) do
    socket =
      socket
      |> refresh_steps(name, state)
      |> refresh_state()

    # Automatically proceed to next step for certain conditions
    socket =
      cond do
        # Auto-progress for template repository creation (not selection)
        name == :template_repository and Map.has_key?(state, :template_repo_name) ->
          current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))
          next_step = Enum.at(socket.assigns.steps, current_index + 1)
          assign(socket, :current_step, next_step)

        # Auto-progress for template files when finalized via "Next" button
        name == :template_files ->
          current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))
          next_step = Enum.at(socket.assigns.steps, current_index + 1)
          assign(socket, :current_step, next_step)

        true ->
          socket
      end

    {:noreply, socket}
  end

  def handle_info({:repo_tree, component_name, message}, socket) do
    send_update(component_module(component_name), id: component_name, repo_tree_message: message)
    {:noreply, socket}
  end

  def handle_info({:files, component_name, message}, socket) do
    send_update(component_module(component_name), id: component_name, files_message: message)
    {:noreply, socket}
  end

  def handle_info({:create_source_files, selected_files, template_repo}, socket) do
    send_update(Steps.Summary,
      id: :summary,
      repo_tree_message: {:create_source_files, selected_files, template_repo}
    )

    {:noreply, socket}
  end

  def handle_info({:push_to_github, source_files, template_repo}, socket) do
    send_update(Steps.Summary,
      id: :summary,
      repo_tree_message: {:push_to_github, source_files, template_repo}
    )

    {:noreply, socket}
  end

  def handle_info({:github_commit_result, status, source_files, template_repo}, socket) do
    if socket.assigns.current_step.name == :summary do
      send_update(Steps.Summary,
        id: socket.assigns.current_step.name,
        github_commit_result: {status, source_files, template_repo}
      )
    else
      Logger.warning(
        "Received :github_commit_result when current step is not Summary: #{inspect(socket.assigns.current_step.name)} - Status: #{inspect(status)}"
      )
    end

    {:noreply, socket}
  end

  def handle_info({:repository_created, repo_data}, socket) do
    # Forward repository creation events to the template repository step if it's active
    if socket.assigns.current_step.name == :template_repository do
      send_update(Steps.TemplateRepository,
        id: :template_repository,
        repository_created: repo_data
      )
    end

    {:noreply, socket}
  end

  defp component_module(component_name) do
    Keyword.get(@steps, component_name)
  end

  defp refresh_steps(socket, name, state) do
    finishing_step = get_step(name, socket.assigns.steps)
    current_index = Enum.find_index(socket.assigns.steps, &(&1.name == name))

    updated_steps =
      List.replace_at(socket.assigns.steps, current_index, %{finishing_step | state: state})

    assign(socket, :steps, updated_steps)
  end

  defp refresh_state(socket) do
    assign(socket, :state, fresh_state(socket.assigns.steps))
  end

  defp get_step(name, steps) do
    Enum.find(steps, fn step -> step.name == name end)
  end

  defp build_steps do
    Enum.map(@steps, fn {name, component} ->
      %Step{name: name, component: component, state: %{}}
    end)
  end

  defp fresh_state(steps) do
    Enum.reduce(steps, %{}, fn step, acc -> Map.merge(acc, step.state) end)
  end
end
