defmodule RepobotWeb.Live.Settings.Index do
  @moduledoc false
  use RepobotWeb, :live_view

  alias Repobot.Accounts

  def mount(_params, session, socket) do
    current_user = Accounts.get_user!(session["current_user_id"])
    {:ok, organizations} = Accounts.get_user_organizations_from_db(current_user)

    socket =
      socket
      |> assign(:current_user, current_user)
      |> assign(:page_title, "Settings")
      |> assign(:organizations, organizations)
      |> assign(:selected_org_id, current_user.default_organization_id)
      |> assign_organization_changeset(current_user.default_organization_id)

    {:ok, socket}
  end

  def handle_event("select_organization", %{"id" => org_id}, socket) do
    # Find the organization to verify it exists and is accessible to the user
    case Enum.find(socket.assigns.organizations, &(&1.id == org_id)) do
      nil ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid organization selected")}

      _org ->
        {:noreply,
         socket
         |> assign(:selected_org_id, org_id)
         |> assign_organization_changeset(org_id)}
    end
  end

  def handle_event("save_settings", %{"organization_settings" => settings_params}, socket) do
    organization =
      Enum.find(socket.assigns.organizations, &(&1.id == socket.assigns.selected_org_id))

    case Accounts.update_organization_settings(organization, settings_params) do
      {:ok, settings} ->
        # Update the organization in the list with the new settings
        organizations =
          Enum.map(socket.assigns.organizations, fn org ->
            if org.id == organization.id do
              %{org | settings: settings}
            else
              org
            end
          end)

        {:noreply,
         socket
         |> assign(:organizations, organizations)
         |> assign_organization_changeset(organization.id)
         |> put_flash(:info, "Settings updated successfully")}

      {:error, changeset} ->
        {:noreply,
         socket
         |> assign(:settings_changeset, changeset)
         |> put_flash(:error, "Failed to update settings")}
    end
  end

  defp assign_organization_changeset(socket, org_id) do
    organization = Enum.find(socket.assigns.organizations, &(&1.id == org_id))

    settings =
      organization.settings ||
        %Repobot.Accounts.OrganizationSettings{organization_id: organization.id}

    changeset = Repobot.Accounts.OrganizationSettings.changeset(settings, %{})
    assign(socket, :settings_changeset, changeset)
  end

  def render(assigns) do
    ~H"""
    <div class="p-6">
      <div class="flex justify-between items-center mb-8">
        <h1 class="text-2xl font-semibold text-slate-900">Settings</h1>
      </div>

      <div class="bg-white shadow rounded-lg divide-y divide-slate-200 max-w-3xl mx-auto">
        <div class="px-6 py-5">
          <h2 class="text-lg font-medium text-slate-900">GitHub Profile Information</h2>
          <p class="mt-1 text-sm text-slate-500">
            Information from your GitHub account.
          </p>
        </div>

        <div class="px-6 py-5">
          <div class="grid grid-cols-1 md:grid-cols-6 gap-6">
            <div class="md:col-span-6 flex items-center gap-4">
              <img
                src={@current_user.info.avatar_url}
                alt={@current_user.info.name || @current_user.login}
                class="h-16 w-16 rounded-full"
              />
              <div>
                <h3 class="text-lg font-medium text-slate-900">
                  {@current_user.info.name || @current_user.login}
                </h3>
                <p class="text-sm text-slate-500">
                  <a
                    href={@current_user.info.html_url}
                    target="_blank"
                    class="text-indigo-600 hover:text-indigo-800"
                  >
                    @{@current_user.login}
                  </a>
                </p>
              </div>
            </div>

            <div class="md:col-span-3">
              <label class="block text-sm font-medium text-slate-700">Name</label>
              <div class="mt-1">
                <input
                  type="text"
                  readOnly
                  value={@current_user.info.name}
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-slate-50"
                />
              </div>
            </div>

            <div class="md:col-span-3">
              <label class="block text-sm font-medium text-slate-700">Username</label>
              <div class="mt-1">
                <input
                  type="text"
                  readOnly
                  value={@current_user.login}
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-slate-50"
                />
              </div>
            </div>

            <div class="md:col-span-6">
              <label class="block text-sm font-medium text-slate-700">Email</label>
              <div class="mt-1">
                <input
                  type="email"
                  readOnly
                  value={@current_user.info.email}
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-slate-50"
                />
              </div>
            </div>

            <div class="md:col-span-6">
              <label class="block text-sm font-medium text-slate-700">Bio</label>
              <div class="mt-1">
                <textarea
                  readOnly
                  rows="3"
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-slate-50"
                ><%= @current_user.info.description %></textarea>
              </div>
            </div>

            <div class="md:col-span-3">
              <label class="block text-sm font-medium text-slate-700">Location</label>
              <div class="mt-1">
                <input
                  type="text"
                  readOnly
                  value={@current_user.info.location}
                  class="block w-full rounded-md border-slate-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-slate-50"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="px-6 py-5">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-lg font-medium text-slate-900">API Settings</h2>
              <p class="mt-1 text-sm text-slate-500">
                Manage your API keys for various services.
              </p>
            </div>
            <div class="relative">
              <.form for={%{}} phx-change="select_organization">
                <select
                  name="id"
                  class="block w-full rounded-md border-slate-300 pr-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
                >
                  <%= for org <- @organizations do %>
                    <option value={org.id} selected={org.id == @selected_org_id}>
                      {org.name}
                    </option>
                  <% end %>
                </select>
              </.form>
            </div>
          </div>

          <.form :let={f} for={@settings_changeset} phx-submit="save_settings" class="mt-6 space-y-6">
            <div>
              <label class="block text-sm font-medium text-slate-700">Anthropic API Key</label>
              <div class="mt-1">
                <.masked_api_key_input
                  field={f[:anthropic_api_key]}
                  placeholder="Enter your Anthropic API key"
                  class="block w-full"
                />
              </div>
              <p class="mt-1 text-sm text-slate-500">Used for Claude AI integration.</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-slate-700">OpenAI API Key</label>
              <div class="mt-1">
                <.masked_api_key_input
                  field={f[:openai_api_key]}
                  placeholder="Enter your OpenAI API key"
                  class="block w-full"
                />
              </div>
              <p class="mt-1 text-sm text-slate-500">Used for GPT integration.</p>
            </div>

            <div class="flex justify-end">
              <.button>Save Settings</.button>
            </div>
          </.form>
        </div>
      </div>
    </div>
    """
  end
end
