<!DOCTYPE html>
<html lang="en" class="[scrollbar-gutter:stable]">
  <head>
    <.live_title suffix=" · RepoBot">
      {assigns[:page_title] || "RepoBot"}
    </.live_title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
    {Application.get_env(:live_debugger, :live_debugger_tags)}
  </head>
  <body class="bg-slate-50 min-h-screen">
    <nav class="bg-slate-800 border-b border-slate-700">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center gap-8">
            <img src={~p"/images/repobot.png"} alt="RepoBot" class="h-12" />
            <div class="hidden md:flex items-center gap-6">
              <.link
                navigate={~p"/dashboard"}
                class="text-slate-200 hover:text-indigo-300 px-3 py-2 text-sm font-medium transition-colors duration-150"
              >
                Dashboard
              </.link>
              <.link
                navigate={~p"/repositories"}
                class="text-slate-200 hover:text-indigo-300 px-3 py-2 text-sm font-medium transition-colors duration-150"
              >
                Repositories
              </.link>
              <.link
                navigate={~p"/source-files"}
                class="text-slate-200 hover:text-indigo-300 px-3 py-2 text-sm font-medium transition-colors duration-150"
              >
                Source Files
              </.link>
              <.link
                navigate={~p"/sync-map"}
                class="text-slate-200 hover:text-indigo-300 px-3 py-2 text-sm font-medium transition-colors duration-150"
              >
                Sync Map
              </.link>
            </div>
          </div>

          <div class="flex items-center gap-4">
            <.user_menu current_user={@current_user} current_organization={@current_organization} />
          </div>
        </div>
      </div>
    </nav>
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <.github_app_installation_banner
        github_app_installation_required={@github_app_installation_required}
        github_app_install_url={@github_app_install_url}
      />
      <div class="bg-white rounded-xl shadow-sm border border-slate-100">
        {@inner_content}
      </div>
    </main>
  </body>
</html>
