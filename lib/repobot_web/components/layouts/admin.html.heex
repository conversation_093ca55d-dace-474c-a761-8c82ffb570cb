<nav class="bg-indigo-600 border-b border-indigo-700">
  <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <div class="flex items-center gap-8">
        <div class="text-white font-semibold text-lg">Repobot Admin</div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-center space-x-4">
            <.link
              navigate={~p"/admin"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-home" class="mr-1.5 h-4 w-4" />
                <span>Dashboard</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/users"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-users" class="mr-1.5 h-4 w-4" />
                <span>Users</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/organizations"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-building-office" class="mr-1.5 h-4 w-4" />
                <span>Organizations</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/repositories"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-code-bracket" class="mr-1.5 h-4 w-4" />
                <span>Repositories</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/source-files"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-document" class="mr-1.5 h-4 w-4" />
                <span>Source Files</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/events"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-bell" class="mr-1.5 h-4 w-4" />
                <span>Events</span>
              </div>
            </.link>

            <.link
              navigate={~p"/admin/waitlist"}
              class="text-white hover:bg-indigo-500 hover:bg-opacity-75 px-3 py-2 rounded-md text-sm font-medium"
            >
              <div class="flex items-center">
                <.icon name="hero-queue-list" class="mr-1.5 h-4 w-4" />
                <span>Waitlist</span>
              </div>
            </.link>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>

<!-- Mobile menu dropdown, appears when screen size is less than md breakpoint -->
<div class="md:hidden bg-indigo-100 border-b border-indigo-200">
  <div class="px-2 py-2">
    <div class="flex flex-col space-y-1">
      <.link
        navigate={~p"/admin"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-home" class="mr-2 h-5 w-5" />
          <span>Dashboard</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/users"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-users" class="mr-2 h-5 w-5" />
          <span>Users</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/organizations"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-building-office" class="mr-2 h-5 w-5" />
          <span>Organizations</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/repositories"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-code-bracket" class="mr-2 h-5 w-5" />
          <span>Repositories</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/source-files"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-document" class="mr-2 h-5 w-5" />
          <span>Source Files</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/events"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-bell" class="mr-2 h-5 w-5" />
          <span>Events</span>
        </div>
      </.link>

      <.link
        navigate={~p"/admin/waitlist"}
        class="text-indigo-700 hover:bg-indigo-200 px-3 py-2 rounded-md text-sm font-medium"
      >
        <div class="flex items-center">
          <.icon name="hero-queue-list" class="mr-2 h-5 w-5" />
          <span>Waitlist</span>
        </div>
      </.link>
    </div>
  </div>
</div>

<div class="max-w-full mx-auto px-2 sm:px-4 lg:px-4 py-4">
  <div class="bg-white rounded-xl shadow-sm border border-slate-100">
    {@inner_content}
  </div>
</div>
