defmodule Repobot.Tags do
  @moduledoc """
  The Tags context.
  """

  import Ecto.Query, warn: false
  alias Repobot.Repo
  alias Repobot.Tag

  # Pastel colors that work well with dark text
  @pastel_colors [
    # Reds & Pinks
    "#FFB3BA",
    "#FFC9DE",
    "#FFD1D1",
    "#FFE2E2",
    "#FFCAD4",
    # Greens
    "#BAFFC9",
    "#D4FFB3",
    "#E2FFD1",
    "#DDFFD6",
    "#CAFFBF",
    # Blues
    "#BAE1FF",
    "#BFE9FF",
    "#B3EFFF",
    "#D1F5FF",
    "#CCF2FF",
    # Yellows & Oranges
    "#FFFFBA",
    "#FFE4B3",
    "#FFE8D1",
    "#FFF3BF",
    "#FFEFD1",
    # Purples & Pinks
    "#E5BEFF",
    "#FFB3F7",
    "#F1BEFF",
    "#ECD1FF",
    "#E2D1FF",
    # Cyans & Teals
    "#B3FFF7",
    "#D1FFF8",
    "#BFFFF3",
    "#C9FFF9",
    "#D6FFF8",
    # Misc Pastels
    "#FFE8E8",
    "#E8FFF3",
    "#FFF5E8",
    "#F3FFE8",
    "#E8F3FF"
  ]

  @doc """
  Returns a random pastel color from the predefined list.
  """
  def random_pastel_color do
    Enum.random(@pastel_colors)
  end

  @doc """
  Returns a pastel color that hasn't been used by other tags for the same user.
  Falls back to a random color if all colors are used.
  """
  def next_available_color(user_id) do
    used_colors =
      from(t in Tag, where: t.user_id == ^user_id, select: t.color)
      |> Repo.all()
      |> MapSet.new()

    available_colors = MapSet.new(@pastel_colors) |> MapSet.difference(used_colors)

    if MapSet.size(available_colors) > 0 do
      available_colors |> MapSet.to_list() |> Enum.random()
    else
      random_pastel_color()
    end
  end

  @doc """
  Returns the list of tags for a given user.
  """
  def list_tags(user) do
    Tag
    |> where(user_id: ^user.id)
    |> order_by(:name)
    |> Repo.all()
  end

  @doc """
  Returns the list of tags that have associated source files for a given user.
  """
  def list_tags_with_source_files(user) do
    Tag
    |> join(:inner, [t], sf in assoc(t, :source_files))
    |> where([t], t.user_id == ^user.id)
    |> distinct([t], t.id)
    |> order_by([t], t.name)
    |> Repo.all()
  end

  @doc """
  Returns a list of tag names for a given organization.
  """
  def list_organization_tag_names(organization_id) do
    Tag
    |> where(organization_id: ^organization_id)
    |> select([t], t.name)
    |> order_by(:name)
    |> Repo.all()
  end

  @doc """
  Gets a single tag.
  Raises `Ecto.NoResultsError` if the Tag does not exist.
  """
  def get_tag!(id), do: Repo.get!(Tag, id)

  @doc """
  Creates a tag.
  """
  def create_tag(attrs \\ %{}) do
    %Tag{}
    |> Tag.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a tag.
  """
  def update_tag(%Tag{} = tag, attrs) do
    tag
    |> Tag.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a tag.
  """
  def delete_tag(%Tag{} = tag) do
    Repo.delete(tag)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tag changes.
  """
  def change_tag(%Tag{} = tag, attrs \\ %{}) do
    Tag.changeset(tag, attrs)
  end

  @doc """
  Gets or creates tags with the given names for a user.
  Prioritizes existing tags with similar names to avoid duplication.
  """
  def get_or_create_tags(names, user) when is_list(names) do
    # First try to get exact matching tags
    existing_tags = Repo.all(from t in Tag, where: t.name in ^names and t.user_id == ^user.id)
    existing_names = Enum.map(existing_tags, & &1.name)

    # Get all user's tags for similarity matching
    all_user_tags = Repo.all(from t in Tag, where: t.user_id == ^user.id)
    all_tag_names = Enum.map(all_user_tags, & &1.name)

    # For each new tag name, check if there's a similar existing tag
    new_tags =
      names
      |> Enum.reject(&(&1 in existing_names))
      |> Enum.map(fn name ->
        # Try to find a similar existing tag
        similar_tag = find_similar_tag(name, all_tag_names, all_user_tags)

        case similar_tag do
          nil ->
            # No similar tag found, create a new one
            {:ok, tag} =
              %Tag{}
              |> Tag.changeset(%{
                name: name,
                user_id: user.id,
                organization_id: user.default_organization_id
              })
              |> Repo.insert()

            tag

          tag ->
            # Use the existing similar tag
            tag
        end
      end)

    existing_tags ++ new_tags
  end

  def get_or_create_tags(_, _), do: []

  # Helper function to find similar tags using string similarity
  defp find_similar_tag(name, existing_names, existing_tags) do
    normalized_name = normalize_tag_name(name)

    # First try to find an exact match with normalized names
    exact_match =
      Enum.find(existing_names, fn existing ->
        normalize_tag_name(existing) == normalized_name
      end)

    case exact_match do
      nil ->
        # No exact match, look for similar tags
        similar_name =
          existing_names
          |> Enum.map(fn existing ->
            similarity = String.jaro_distance(normalized_name, normalize_tag_name(existing))
            {existing, similarity}
          end)
          |> Enum.filter(fn {_, similarity} -> similarity > 0.8 end)
          |> Enum.max_by(fn {_, similarity} -> similarity end, fn -> nil end)

        case similar_name do
          nil -> nil
          {name, _} -> Enum.find(existing_tags, &(&1.name == name))
        end

      name ->
        # Found exact match
        Enum.find(existing_tags, &(&1.name == name))
    end
  end

  # Helper function to normalize tag names for comparison
  defp normalize_tag_name(name) do
    name
    |> String.downcase()
    |> String.replace(~r/[-_\s]+/, "")
  end
end
