defmodule Repobot.GitHub do
  @moduledoc """
  Behaviour and implementation for GitHub API interactions.
  """

  require Logger

  # Add rate limit constants
  # 50ms delay between requests
  @default_delay 50
  # 5 second delay when rate limit is low
  @rate_limit_delay 5000

  @callback user_repos(client :: any(), username :: String.t()) ::
              {:ok, list(map())} | {:error, String.t()}

  @callback get_repo(client :: any(), owner :: String.t(), repo :: String.t()) ::
              {integer(), map() | nil, map()}

  @callback get_file_content(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t()
            ) ::
              {:ok, binary(), map()} | {:error, String.t()}

  @callback get_file_content(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t(),
              ref :: String.t() | nil
            ) ::
              {:ok, binary(), map()} | {:error, String.t()}

  @callback get_tree(client :: any(), owner :: String.t(), repo :: String.t()) ::
              {:ok, list(map())} | {:error, String.t()}

  @callback get_tree(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t()
            ) ::
              {:ok, list(map())} | {:error, String.t()}

  @callback get_installation_token(owner :: String.t(), repo :: String.t()) ::
              {:ok, String.t()} | {:error, String.t()}

  @callback generate_jwt() :: {:ok, String.t()} | {:error, String.t()}

  @callback client() :: any()
  @callback client(user :: struct()) :: any()
  @callback client(user :: struct() | nil | String.t(), repo :: any()) :: any()

  @callback get_file_status(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t()
            ) ::
              {:ok, :exists | :not_found} | {:error, String.t()}

  @callback get_ref(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              ref :: String.t()
            ) :: {integer(), map() | nil, map()}

  @callback create_ref(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              ref :: String.t(),
              sha :: String.t()
            ) :: {integer(), map() | nil, map()}

  @callback create_file(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t(),
              content :: String.t(),
              message :: String.t(),
              branch :: String.t() | nil
            ) :: {integer(), map() | nil, map()}

  @callback update_file(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              path :: String.t(),
              content :: String.t(),
              message :: String.t(),
              sha :: String.t(),
              branch :: String.t() | nil
            ) :: {integer(), map() | nil, map()}

  @callback create_pull_request(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              title :: String.t(),
              body :: String.t(),
              head :: String.t(),
              base :: String.t()
            ) :: {integer(), map() | nil, map()}

  @callback list_user_installations(client :: any()) ::
              {:ok, list(map())} | {:error, String.t()}

  @callback get_organization_membership(
              client :: any(),
              org_login :: String.t(),
              user_login :: String.t()
            ) ::
              {:ok, map()} | {:error, String.t() | :not_found}

  @callback list_repos(client :: any(), org :: String.t()) ::
              {:ok, list(map())} | {:error, String.t()}

  @callback create_commit(repo :: struct(), message :: String.t(), files :: list(map())) ::
              {:ok, map()} | {:error, String.t()}

  @callback create_tree(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              tree_entries :: list(map()),
              base_tree :: String.t() | nil
            ) ::
              {integer(), map() | nil, map()}

  @callback create_commit(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              message :: String.t(),
              tree_sha :: String.t(),
              parent_shas :: list(String.t())
            ) ::
              {integer(), map() | nil, map()}

  @callback update_reference(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              ref :: String.t(),
              sha :: String.t(),
              force :: boolean() | nil
            ) ::
              {integer(), map() | nil, map()}

  @callback get_commit(
              client :: any(),
              owner :: String.t(),
              repo :: String.t(),
              sha :: String.t()
            ) ::
              {integer(), map() | nil, map()}

  # Default implementation
  def user_repos(client, username) do
    try do
      Logger.debug("Starting to fetch owned repositories for user #{username}")
      # Start with page 1 and collect all repositories
      result =
        Stream.iterate(1, &(&1 + 1))
        |> Enum.reduce_while([], fn page, acc ->
          Logger.debug("Fetching page #{page} (accumulated #{length(acc)} repos so far)")

          case Tentacat.Repositories.list_mine(client,
                 page: page,
                 per_page: 100,
                 # Only fetch repos owned by the user, not collaborations or org repos
                 affiliation: "owner",
                 visibility: "all"
               ) do
            {200, repos, _} when repos != [] ->
              Logger.debug("Received #{length(repos)} repositories on page #{page}")

              Logger.debug(
                "Private repos in response: #{inspect(Enum.filter(repos, & &1["private"]))}"
              )

              # No need to filter by owner since we're only requesting owner repos
              {:cont, acc ++ repos}

            {200, [], _} ->
              Logger.debug("Received empty page #{page}, stopping pagination")
              # No more repositories
              {:halt, acc}

            {401, body, _} ->
              Logger.error("Unauthorized while fetching page #{page}: #{inspect(body)}")
              {:halt, {:error, "Unauthorized: #{inspect(body)}"}}

            {403, body, _} ->
              Logger.error("Forbidden while fetching page #{page}: #{inspect(body)}")
              {:halt, {:error, "Forbidden: #{inspect(body)}"}}

            {status, body, _} ->
              Logger.error("GitHub API error on page #{page}: #{status} - #{inspect(body)}")
              {:halt, {:error, "GitHub API error: #{status} - #{inspect(body)}"}}
          end
        end)
        |> case do
          {:error, _} = error ->
            Logger.error("Failed to fetch repositories: #{inspect(error)}")
            error

          repos when is_list(repos) ->
            # Find user and check their default organization's private_repos setting
            case Repobot.Repo.get_by(Repobot.Accounts.User, login: username)
                 |> Repobot.Repo.preload(:default_organization) do
              %{default_organization: %{private_repos: true}} = user ->
                # Include all repos
                Logger.debug(
                  "User #{username}'s default organization (#{user.default_organization.name}) has private_repos enabled, including all repositories"
                )

                Logger.debug("Total repos before filtering: #{length(repos)}")

                Logger.debug(
                  "Private repos count: #{length(Enum.filter(repos, & &1["private"]))}"
                )

                {:ok, repos}

              user ->
                # Filter out private repos
                Logger.debug(
                  "User #{username}'s default organization #{inspect(user.default_organization)} has private_repos disabled or not set, filtering out private repositories"
                )

                Logger.debug("Total repos before filtering: #{length(repos)}")

                Logger.debug(
                  "Private repos being filtered out: #{length(Enum.filter(repos, & &1["private"]))}"
                )

                {:ok, Enum.reject(repos, & &1["private"])}
            end
        end

      Logger.debug("Final result: #{inspect(result, pretty: true)}")
      result
    rescue
      e ->
        Logger.error("Failed to fetch user repositories: #{inspect(e)}")
        {:error, "Failed to fetch user repositories"}
    end
  end

  def list_repos(client, org) do
    try do
      Logger.debug("Starting to fetch repositories for organization #{org}")
      # Start with page 1 and collect all repositories
      result =
        Stream.iterate(1, &(&1 + 1))
        |> Enum.reduce_while([], fn page, acc ->
          Logger.debug("Fetching page #{page} (accumulated #{length(acc)} repos so far)")

          case Tentacat.Repositories.list_orgs(client, org,
                 page: page,
                 per_page: 100,
                 # This will fetch both public and private repos
                 type: "all"
               ) do
            {200, repos, _} when repos != [] ->
              Logger.debug("Received #{length(repos)} repositories on page #{page}")
              {:cont, acc ++ repos}

            {200, [], _} ->
              Logger.debug("Received empty page #{page}, stopping pagination")
              # No more repositories
              {:halt, acc}

            {401, body, _} ->
              Logger.error("Unauthorized while fetching page #{page}: #{inspect(body)}")
              {:halt, {:error, "Unauthorized: #{inspect(body)}"}}

            {403, body, _} ->
              Logger.error("Forbidden while fetching page #{page}: #{inspect(body)}")
              {:halt, {:error, "Forbidden: #{inspect(body)}"}}

            {status, body, _} ->
              Logger.error("GitHub API error on page #{page}: #{status} - #{inspect(body)}")
              {:halt, {:error, "GitHub API error: #{status} - #{inspect(body)}"}}
          end
        end)
        |> case do
          {:error, _} = error ->
            Logger.error("Failed to fetch repositories: #{inspect(error)}")
            error

          repos when is_list(repos) ->
            # Filter private repos based on organization settings
            case Repobot.Repo.get_by(Repobot.Accounts.Organization, name: org) do
              %{private_repos: true} ->
                # Include all repos
                Logger.debug(
                  "Organization #{org} has private_repos enabled, including all repositories"
                )

                {:ok, repos}

              _ ->
                # Filter out private repos
                Logger.debug(
                  "Organization #{org} has private_repos disabled, filtering out private repositories"
                )

                {:ok, Enum.reject(repos, & &1["private"])}
            end
        end

      Logger.debug("Final result: #{inspect(result, pretty: true)}")
      result
    rescue
      e ->
        Logger.error("Failed to fetch organization repositories: #{inspect(e)}")
        {:error, "Failed to fetch organization repositories"}
    end
  end

  def get_repo(client, owner, repo) do
    Tentacat.Repositories.repo_get(client, owner, repo)
  end

  def get_file_content(client, owner, repo, path, ref \\ nil) do
    # Add delay between requests
    Process.sleep(@default_delay)

    result =
      if ref do
        Tentacat.Contents.find_in(client, owner, repo, path, ref)
      else
        Tentacat.Contents.find(client, owner, repo, path)
      end

    case result do
      {200, %{"type" => "file", "content" => content} = response,
       %HTTPoison.Response{headers: headers}} ->
        # Log remaining rate limit after successful request
        log_remaining_rate_limit(headers)

        # Check if we're running low on rate limit and need to slow down
        maybe_delay_for_rate_limit(headers)

        # GitHub returns content with newlines, need to remove them before decoding
        decoded =
          content
          |> String.replace("\n", "")
          |> Base.decode64!()

        {:ok, decoded, response}

      # Handle case where API returns 200 but it's a directory listing
      {200, body, %HTTPoison.Response{headers: headers}} when is_list(body) ->
        log_remaining_rate_limit(headers)
        Logger.warning("Requested file path is a directory: #{path}")
        {:error, :path_is_directory}

      # Handle case where API returns 200 but it's a Git submodule
      {200, %{"type" => "submodule"} = _body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        {:error, :submodule}

      {404, _body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        {:error, :file_not_found}

      {403, body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)

        Logger.warning(
          "Rate limit exceeded while fetching #{path} from #{owner}/#{repo}: #{inspect(body)}"
        )

        Process.sleep(@rate_limit_delay)
        {:error, :rate_limit_exceeded}

      {status, body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        {:error, "GitHub API error: #{status} - #{inspect(body)}"}
    end
  end

  def get_tree(client, owner, repo, path \\ "/") do
    get_tree_recursive(client, owner, repo, path)
  end

  defp get_tree_recursive(client, owner, repo, path) do
    # Add delay between requests
    Process.sleep(@default_delay)

    case Tentacat.Contents.find(client, owner, repo, path) do
      {200, items, %HTTPoison.Response{headers: headers}} when is_list(items) ->
        # Log remaining rate limit after successful request
        log_remaining_rate_limit(headers)

        # Check if we need to slow down
        maybe_delay_for_rate_limit(headers)

        # Process each item and recursively get contents of directories
        items_with_children =
          Enum.reduce(items, [], fn item, acc ->
            item_data = %{
              "name" => item["name"],
              "path" => item["path"],
              "type" => if(item["type"] == "dir", do: "dir", else: "file"),
              "size" => item["size"],
              "sha" => item["sha"]
            }

            if item["type"] == "dir" do
              case get_tree_recursive(client, owner, repo, item["path"]) do
                {:ok, children} -> [item_data | children] ++ acc
                {:error, _} -> [item_data | acc]
              end
            else
              [item_data | acc]
            end
          end)

        {:ok, items_with_children}

      {200, %{"message" => message}, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        Logger.error("GitHub API error: #{message}")
        {:error, message}

      {404, _body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        # Return empty list for empty repositories or non-existent paths
        if path == "/" do
          Logger.info("Repository #{owner}/#{repo} is empty")
          {:ok, []}
        else
          {:error, "Path not found"}
        end

      {403, body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)

        Logger.warning(
          "Rate limit exceeded while fetching tree for #{path} from #{owner}/#{repo}: #{inspect(body)}"
        )

        Process.sleep(@rate_limit_delay)
        {:error, "Rate limit exceeded"}

      {status, body, %HTTPoison.Response{headers: headers}} ->
        log_remaining_rate_limit(headers)
        Logger.error("GitHub API error: #{status} - #{inspect(body)}")
        {:error, "Failed to fetch directory contents: #{status} - #{inspect(body)}"}
    end
  end

  @doc """
  Gets an installation access token for the GitHub App.
  """
  def get_installation_token(owner, repo) do
    case generate_jwt() do
      {:ok, jwt} ->
        base_req =
          Req.new(
            base_url: "https://api.github.com",
            auth: {:bearer, jwt},
            headers: [
              {"accept", "application/vnd.github+json"},
              {"x-github-api-version", "2022-11-28"}
            ]
          )

        installation_url = "/repos/#{owner}/#{repo}/installation"

        case Req.get(base_req, url: installation_url) do
          {:ok, %{status: 200, body: body}} ->
            installation_id = body["id"]
            token_url = "/app/installations/#{installation_id}/access_tokens"

            case Req.post(base_req, url: token_url) do
              {:ok, %{status: 201, body: token_response}} ->
                {:ok, token_response["token"]}

              {:ok, %{status: _status, body: _error}} ->
                {:error, "Failed to get installation token"}

              {:error, _reason} ->
                {:error, "Failed to get installation token"}
            end

          {:ok, %{status: 404}} ->
            {:error, "GitHub App not installed on repository #{owner}/#{repo}"}

          {:ok, %{status: _status, body: _error}} ->
            {:error, "Failed to find GitHub App installation for #{owner}/#{repo}"}

          {:error, _reason} ->
            {:error, "Failed to find GitHub App installation"}
        end

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Creates a JWT token for GitHub App authentication.
  """
  def generate_jwt do
    app_id = Application.get_env(:repobot, :github_app)[:app_id]
    private_key = Application.get_env(:repobot, :github_app)[:private_key]

    cond do
      !app_id || app_id == 0 ->
        Logger.error("GitHub App ID not configured")
        {:error, "GitHub App ID not configured. Please set GITHUB_APP_ID environment variable."}

      !private_key || private_key == "" ->
        Logger.error("GitHub App private key not configured")
        {:error, "GitHub App private key not configured"}

      true ->
        # JWT token valid for 10 minutes
        now = System.system_time(:second)

        claims = %{
          # Issues 1 minute in the past to avoid clock skew
          "iat" => now - 60,
          # Expires in 10 minutes
          "exp" => now + 600,
          # GitHub expects the iss claim to be a string
          "iss" => to_string(app_id)
        }

        jwk = JOSE.JWK.from_pem(private_key)
        jwt = JOSE.JWT.sign(jwk, %{"alg" => "RS256"}, claims)

        {_, token} = JOSE.JWS.compact(jwt)

        {:ok, token}
    end
  end

  def client(owner \\ nil, repo \\ nil)

  # Handle user struct with token
  def client(%{token: token} = user, _repo) when not is_nil(token) do
    Logger.debug("Creating GitHub client with user access token")
    maybe_refresh_and_create_client(user)
  end

  # Handle user struct without token
  def client(%{} = _user, nil) do
    Logger.debug("Creating GitHub client with JWT (no user token)")
    create_jwt_client()
  end

  # Handle repository-specific client
  def client(owner, repo) when is_binary(owner) and is_binary(repo) do
    Logger.debug("Creating GitHub client for repository #{owner}/#{repo}")

    case get_installation_token(owner, repo) do
      {:ok, token} ->
        Logger.debug("Using installation token for repository access")
        Tentacat.Client.new(%{access_token: token})

      {:error, reason} ->
        Logger.error("Failed to create GitHub client: #{reason}")
        raise "Failed to create GitHub client: #{reason}"
    end
  end

  # Handle app-level client
  def client(nil, nil) do
    Logger.debug("Creating GitHub client for app-level access")
    create_jwt_client()
  end

  defp maybe_refresh_and_create_client(%{token: token, expires_at: expires_at} = user) do
    now = System.system_time(:second)
    should_refresh = expires_at && now >= expires_at - 60

    if should_refresh do
      case Repobot.Accounts.refresh_user_token(user) do
        {:ok, refreshed_user} ->
          Logger.debug("Successfully refreshed user token")
          Tentacat.Client.new(%{access_token: refreshed_user.token})

        # Handle both error tuple and refreshed user struct directly
        %{token: new_token} when not is_nil(new_token) ->
          Logger.debug("Successfully refreshed user token")
          Tentacat.Client.new(%{access_token: new_token})

        {:error, reason} ->
          Logger.error("Failed to refresh user token: #{inspect(reason)}")
          # Fall back to existing token
          Tentacat.Client.new(%{access_token: token})

        _ ->
          Logger.error("Unexpected response when refreshing token")
          # Fall back to existing token
          Tentacat.Client.new(%{access_token: token})
      end
    else
      Logger.debug("Token still valid, using existing token")
      Tentacat.Client.new(%{access_token: token})
    end
  end

  defp create_jwt_client do
    case generate_jwt() do
      {:ok, jwt} ->
        Logger.debug("Using JWT for app-level access")
        Tentacat.Client.new(%{jwt: jwt})

      {:error, reason} ->
        Logger.error("Failed to create GitHub client: #{reason}")
        raise "Failed to create GitHub client: #{reason}"
    end
  end

  @doc """
  Gets the status of a file in a repository.
  Returns:
  - {:ok, :exists} if the file exists
  - {:ok, :not_found} if the file doesn't exist
  - {:error, reason} if there was an error checking the file
  """
  def get_file_status(client, owner, repo, path) do
    case Tentacat.Contents.find(client, owner, repo, path) do
      {200, _, _} -> {:ok, :exists}
      {404, _, _} -> {:ok, :not_found}
      {status, body, _} -> {:error, "Failed to check file status: #{status} - #{inspect(body)}"}
    end
  end

  def get_ref(client, owner, repo, ref) do
    Tentacat.References.find(client, owner, repo, ref)
  end

  def create_ref(client, owner, repo, ref, sha) do
    body = %{ref: ref, sha: sha}
    Tentacat.References.create(client, owner, repo, body)
  end

  def create_file(client, owner, repo, path, content, message, branch) do
    body = %{
      message: message,
      content: content
    }

    # Only add branch if specified
    body = if branch, do: Map.put(body, :branch, branch), else: body

    Tentacat.Contents.create(client, owner, repo, path, body)
  end

  def update_file(client, owner, repo, path, content, message, sha, branch) do
    body = %{
      message: message,
      content: content,
      sha: sha
    }

    # Only add branch if specified
    body = if branch, do: Map.put(body, :branch, branch), else: body

    Tentacat.Contents.update(client, owner, repo, path, body)
  end

  @doc """
  Create a Git tree with multiple entries, useful for batch operations like
  creating multiple files in a single commit.
  """
  def create_tree(client, owner, repo, tree_entries, base_tree \\ nil) do
    # There is no native Tentacat function for this, so we use a custom request
    url = "repos/#{owner}/#{repo}/git/trees"

    # Prepare the request body
    body = %{
      "tree" => tree_entries
    }

    # Add base_tree if provided
    body = if base_tree, do: Map.put(body, "base_tree", base_tree), else: body

    # Make the request
    Tentacat.post(url, client, body)
  end

  @doc """
  Create a Git commit with a given tree.
  """
  def create_commit(client, owner, repo, message, tree_sha, parent_shas) do
    # There is no native Tentacat function for this, so we use a custom request
    url = "repos/#{owner}/#{repo}/git/commits"

    # Prepare the request body
    body = %{
      "message" => message,
      "tree" => tree_sha,
      "parents" => parent_shas
    }

    # Make the request
    Tentacat.post(url, client, body)
  end

  @doc """
  Update a Git reference (like a branch) to point to a new commit.
  """
  def update_reference(client, owner, repo, ref, sha, force \\ false) do
    # There is no native Tentacat function for this, so we use a custom request
    url = "repos/#{owner}/#{repo}/git/refs/#{ref}"

    # Prepare the request body
    body = %{
      "sha" => sha,
      "force" => force
    }

    # Make the request
    Tentacat.patch(url, client, body)
  end

  @doc """
  Get a Git commit by SHA.
  """
  def get_commit(client, owner, repo, sha) do
    # There is no native Tentacat function for this, so we use a custom request
    url = "repos/#{owner}/#{repo}/git/commits/#{sha}"

    # Make the request
    Tentacat.get(url, client)
  end

  def create_pull_request(client, owner, repo, title, body, head, base) do
    pr_body = %{
      title: title,
      body: body,
      head: head,
      base: base
    }

    Tentacat.Pulls.create(client, owner, repo, pr_body)
  end

  @doc """
  Gets a list of organizations where the GitHub App is installed for the authenticated user.
  Returns {:ok, installations} on success or {:error, reason} on failure.
  """
  def list_user_installations(client) do
    # Extract the user token directly
    token =
      if Map.has_key?(client.auth, :access_token),
        do: client.auth.access_token,
        else: client.auth.token

    Logger.debug("Fetching user installations with token: #{String.slice(token, 0, 5)}...")

    headers = [
      {"accept", "application/vnd.github+json"},
      {"authorization", "token #{token}"},
      {"x-github-api-version", "2022-11-28"}
    ]

    case Req.get(
           "https://api.github.com/user/installations",
           headers: headers
         ) do
      {:ok, %{status: 200, body: %{"installations" => installations}}} ->
        # Return raw installations, role check will happen in Accounts
        Logger.debug("Successfully retrieved #{length(installations)} installations")
        {:ok, installations}

      {:ok, %{status: 200, body: body}} ->
        # Some unexpected body format that doesn't contain "installations"
        Logger.error("GitHub API returned unexpected response structure: #{inspect(body)}")
        {:error, "Unexpected response structure: #{inspect(body)}"}

      {:ok, %{status: 401, body: body}} ->
        Logger.error("GitHub API unauthorized error (401): #{inspect(body)}")
        {:error, "Unauthorized: #{inspect(body)}"}

      {:ok, %{status: 403, body: body}} ->
        Logger.error("GitHub API forbidden error (403): #{inspect(body)}")
        {:error, "Forbidden: #{inspect(body)}"}

      {:ok, %{status: status, body: body}} ->
        Logger.error("GitHub API error (#{status}): #{inspect(body)}")
        {:error, "Failed to fetch installations: #{status} - #{inspect(body)}"}

      {:error, reason} ->
        Logger.error("HTTP error fetching installations: #{inspect(reason)}")
        {:error, "Failed to fetch installations"}
    end
  end

  @doc """
  Gets the user's membership details for a specific organization.
  Returns {:ok, membership_details} or {:error, :not_found | reason}.
  """
  def get_organization_membership(client, org_login, user_login) do
    # Add delay between requests
    Process.sleep(@default_delay)

    # The correct endpoint for checking organization membership
    url = "https://api.github.com/orgs/#{org_login}/memberships/#{user_login}"

    # We must use the user's personal access token to check their own membership status
    # This is a GitHub API requirement - the app token cannot see non-public memberships
    token =
      if Map.has_key?(client.auth, :access_token),
        do: client.auth.access_token,
        else: client.auth.token

    headers = [
      {"accept", "application/vnd.github+json"},
      {"authorization", "token #{token}"},
      {"x-github-api-version", "2022-11-28"}
    ]

    Logger.debug("Checking org membership with user token: #{String.slice(token, 0, 5)}...")

    case Req.get(url, headers: headers) do
      {:ok, %{status: 200, body: membership_details, headers: resp_headers}} ->
        log_remaining_rate_limit(resp_headers)
        maybe_delay_for_rate_limit(resp_headers)
        {:ok, membership_details}

      {:ok, %{status: 404, headers: resp_headers}} ->
        log_remaining_rate_limit(resp_headers)
        # User is not a member or org doesn't exist/no access
        {:error, :not_found}

      {:ok, %{status: 403, body: body, headers: resp_headers}} ->
        log_remaining_rate_limit(resp_headers)
        # Potentially rate limit or permission issue
        maybe_delay_for_rate_limit(resp_headers)

        Logger.error(
          "Forbidden error checking membership for #{user_login} in #{org_login}: #{inspect(body)}"
        )

        {:error, :forbidden}

      {:ok, %{status: status, body: body, headers: resp_headers}} ->
        log_remaining_rate_limit(resp_headers)
        Logger.error("GitHub API error getting membership: #{status} - #{inspect(body)}")
        {:error, "API error #{status}"}

      {:error, reason} ->
        Logger.error("HTTP error getting membership: #{inspect(reason)}")
        {:error, "HTTP error"}
    end
  end

  # Add helper functions for rate limit handling
  defp log_remaining_rate_limit(headers) do
    remaining = get_header_value(headers, "X-RateLimit-Remaining")
    reset = get_header_value(headers, "X-RateLimit-Reset")
    limit = get_header_value(headers, "X-RateLimit-Limit")

    if remaining && reset && limit do
      reset_datetime = DateTime.from_unix!(String.to_integer(reset))

      Logger.info(
        "[GitHub Rate Limit] Remaining: #{remaining}/#{limit}, Reset: #{reset_datetime}"
      )
    end
  end

  defp maybe_delay_for_rate_limit(headers) do
    case get_header_value(headers, "X-RateLimit-Remaining") do
      nil ->
        :ok

      remaining ->
        remaining = String.to_integer(remaining)

        if remaining < 100 do
          Logger.warning(
            "[GitHub Rate Limit] Running low on rate limit (#{remaining} remaining). Adding delay."
          )

          Process.sleep(@rate_limit_delay)
        end
    end
  end

  defp get_header_value(headers, key) do
    Enum.find_value(headers, fn
      {^key, value} -> value
      _ -> nil
    end)
  end
end
