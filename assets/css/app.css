@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

@import "../node_modules/diff2html/bundles/css/diff2html.min.css";

/* This file is for your main application CSS */

.glow-border {
  position: relative;
  border-radius: 1rem;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.15) 0%,
      rgba(59, 130, 246, 0.1) 30%,
      rgba(245, 158, 11, 0.1) 70%,
      rgba(251, 146, 60, 0.15) 100%);
  padding: 2px;
  box-shadow:
    -35px 35px 65px -15px rgba(30, 64, 175, 0.25),
    35px -35px 65px -15px rgba(245, 158, 11, 0.25);
}

.glow-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg,
      rgba(30, 64, 175, 0.3) 0%,
      rgba(59, 130, 246, 0.2) 30%,
      rgba(245, 158, 11, 0.2) 70%,
      rgba(251, 146, 60, 0.3) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* Drag and drop styles */
.sortable-ghost {
  opacity: 0.5;
  background-color: #f0f9ff !important;
  /* Light blue background */
  border: 2px dashed #93c5fd !important;
  /* Blue dashed border */
}

.sortable-drag {
  opacity: 0.8;
  transform: scale(0.95);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.sortable-chosen {
  background-color: #f3f4f6 !important;
  /* Light gray background */
}

/* Custom styles for drag handle */
[data-drag-handle] {
  cursor: grab;
}

[data-drag-handle]:active {
  cursor: grabbing;
}
